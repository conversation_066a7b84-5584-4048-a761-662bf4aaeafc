2025-06-06 15:31:35,813 - ERROR - growthhive_logger - [EXCEPTION] message_key: user_creation_failed  
 class-function: AuthOperations-register_user 
 exception: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedTableError'>: relation "users" does not exist
[SQL: SELECT users.id, users.name, users.email, users.mobile_number, users.password_hash, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR]
[parameters: ('<EMAIL>',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
Traceback (most recent call last):
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 533, in _prepare_and_execute
    prepared_stmt, attributes = await adapt_connection._prepare(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 784, in _prepare
    prepared_stmt = await self._connection.prepare(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/asyncpg/connection.py", line 636, in prepare
    return await self._prepare(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/asyncpg/connection.py", line 654, in _prepare
    stmt = await self._get_statement(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/asyncpg/connection.py", line 433, in _get_statement
    statement = await self._protocol.prepare(
  File "asyncpg/protocol/protocol.pyx", line 166, in prepare
asyncpg.exceptions.UndefinedTableError: relation "users" does not exist

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/engine/base.py", line 1969, in _exec_single_context
    self.dialect.do_execute(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/engine/default.py", line 922, in do_execute
    cursor.execute(statement, parameters)
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 591, in execute
    self._adapt_connection.await_(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 125, in await_only
    return current.driver.switch(awaitable)  # type: ignore[no-any-return]
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 185, in greenlet_spawn
    value = await result
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 569, in _prepare_and_execute
    self._handle_exception(error)
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 520, in _handle_exception
    self._adapt_connection._handle_exception(error)
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 808, in _handle_exception
    raise translated_error from error
sqlalchemy.dialects.postgresql.asyncpg.AsyncAdapt_asyncpg_dbapi.ProgrammingError: <class 'asyncpg.exceptions.UndefinedTableError'>: relation "users" does not exist

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/operations/auth/auth_operations.py", line 52, in register_user
    email_check = await db.execute(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/ext/asyncio/session.py", line 455, in execute
    result = await greenlet_spawn(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 190, in greenlet_spawn
    result = context.throw(*sys.exc_info())
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/orm/session.py", line 2308, in execute
    return self._execute_internal(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/orm/session.py", line 2190, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/orm/context.py", line 293, in orm_execute_statement
    result = conn.execute(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/engine/base.py", line 1416, in execute
    return meth(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/sql/elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/engine/base.py", line 1639, in _execute_clauseelement
    ret = self._execute_context(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/engine/base.py", line 1848, in _execute_context
    return self._exec_single_context(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/engine/base.py", line 1988, in _exec_single_context
    self._handle_dbapi_exception(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/engine/base.py", line 2343, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/engine/base.py", line 1969, in _exec_single_context
    self.dialect.do_execute(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/engine/default.py", line 922, in do_execute
    cursor.execute(statement, parameters)
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 591, in execute
    self._adapt_connection.await_(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 125, in await_only
    return current.driver.switch(awaitable)  # type: ignore[no-any-return]
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 185, in greenlet_spawn
    value = await result
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 569, in _prepare_and_execute
    self._handle_exception(error)
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 520, in _handle_exception
    self._adapt_connection._handle_exception(error)
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 808, in _handle_exception
    raise translated_error from error
sqlalchemy.exc.ProgrammingError: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedTableError'>: relation "users" does not exist
[SQL: SELECT users.id, users.name, users.email, users.mobile_number, users.password_hash, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.email = $1::VARCHAR]
[parameters: ('<EMAIL>',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-06 15:45:06,234 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-hash_password | message: Password hashed successfully
2025-06-06 15:45:06,244 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-register_user | message: User registered successfully: <EMAIL>
2025-06-06 15:45:49,033 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-verify_password | message: Password verification completed: True
2025-06-06 15:45:49,042 - INFO - growthhive_logger - [INFO] class-function: JWTHandler-_generate_token | message: JWT token created successfully.
2025-06-06 15:45:49,043 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-login_user | message: User logged in successfully: <EMAIL>
2025-06-06 15:47:04,177 - INFO - growthhive_logger - [INFO] class-function: JWTHandler-decode_token | message: JWT token decoded successfully.
2025-06-06 15:47:04,187 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-get_user_profile | message: User profile retrieved: <EMAIL>
2025-06-06 16:00:34,525 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-verify_password | message: Password verification completed: True
2025-06-06 16:00:34,529 - INFO - growthhive_logger - [INFO] class-function: JWTHandler-_generate_token | message: JWT token created successfully.
2025-06-06 16:00:34,530 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-login_user | message: User logged in successfully: <EMAIL>
2025-06-06 16:11:41,305 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-verify_password | message: Password verification completed: False
2025-06-06 16:11:59,160 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-verify_password | message: Password verification completed: True
2025-06-06 16:11:59,169 - INFO - growthhive_logger - [INFO] class-function: JWTHandler-_generate_token | message: JWT token created successfully.
2025-06-06 16:11:59,171 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-login_user | message: User logged in successfully: <EMAIL>
2025-06-06 16:12:17,453 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
2025-06-06 16:22:35,358 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-verify_password | message: Password verification completed: False
2025-06-06 16:23:37,452 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-verify_password | message: Password verification completed: True
2025-06-06 16:23:37,466 - INFO - growthhive_logger - [INFO] class-function: JWTHandler-_generate_token | message: JWT token created successfully.
2025-06-06 16:23:37,469 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-login_user | message: User logged in successfully: <EMAIL>
2025-06-06 16:33:54,132 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
2025-06-06 16:34:12,778 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
2025-06-06 16:34:23,651 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
2025-06-06 16:39:08,308 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
2025-06-06 16:47:40,084 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
2025-06-06 16:48:12,057 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-verify_password | message: Password verification completed: True
2025-06-06 16:48:12,066 - INFO - growthhive_logger - [INFO] class-function: JWTHandler-_generate_token | message: JWT token created successfully.
2025-06-06 16:48:12,067 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-login_user | message: User logged in successfully: <EMAIL>
2025-06-06 16:48:30,187 - INFO - growthhive_logger - [INFO] class-function: JWTHandler-decode_token | message: JWT token decoded successfully.
2025-06-06 16:48:30,193 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-get_user_profile | message: User profile retrieved: <EMAIL>
2025-06-06 16:48:47,279 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
2025-06-06 16:50:52,204 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-hash_password | message: Password hashed successfully
2025-06-06 16:50:52,216 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-register_user | message: User registered successfully: <EMAIL>
2025-06-06 16:51:19,310 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-verify_password | message: Password verification completed: True
2025-06-06 16:51:19,312 - INFO - growthhive_logger - [INFO] class-function: JWTHandler-_generate_token | message: JWT token created successfully.
2025-06-06 16:51:19,312 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-login_user | message: User logged in successfully: <EMAIL>
2025-06-06 16:51:41,758 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-verify_password | message: Password verification completed: True
2025-06-06 16:51:41,759 - INFO - growthhive_logger - [INFO] class-function: JWTHandler-_generate_token | message: JWT token created successfully.
2025-06-06 16:51:41,759 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-login_user | message: User logged in successfully: <EMAIL>
2025-06-06 17:08:41,755 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-verify_password | message: Password verification completed: True
2025-06-06 17:08:41,764 - INFO - growthhive_logger - [INFO] class-function: JWTHandler-_generate_token | message: JWT token created successfully.
2025-06-06 17:08:41,764 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-login_user | message: User logged in successfully: <EMAIL>
2025-06-06 17:09:00,522 - ERROR - growthhive_logger - [EXCEPTION] message_key: login_failed  
 class-function: AuthOperations-login_user 
 exception: 'ExceptionHandler' object has no attribute 'manage_exception'
Traceback (most recent call last):
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/operations/auth/auth_operations.py", line 170, in login_user
    return await exception_handler_obj.manage_exception(
AttributeError: 'ExceptionHandler' object has no attribute 'manage_exception'
2025-06-06 17:26:45,237 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
2025-06-06 17:29:21,777 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
2025-06-06 17:38:23,101 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-verify_password | message: Password verification completed: True
2025-06-06 17:38:23,108 - INFO - growthhive_logger - [INFO] class-function: JWTHandler-_generate_token | message: JWT token created successfully.
2025-06-06 17:38:23,109 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-login_user | message: User logged in successfully: <EMAIL>
2025-06-06 17:38:58,662 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
2025-06-06 17:46:28,833 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
2025-06-06 17:52:59,897 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-verify_password | message: Password verification completed: True
2025-06-06 17:52:59,906 - INFO - growthhive_logger - [INFO] class-function: JWTHandler-_generate_token | message: JWT token created successfully.
2025-06-06 17:52:59,906 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-login_user | message: User logged in successfully: <EMAIL>
2025-06-06 17:53:10,014 - INFO - growthhive_logger - [INFO] class-function: JWTHandler-decode_token | message: JWT token decoded successfully.
2025-06-06 17:53:10,020 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-get_user_profile | message: User profile retrieved: <EMAIL>
2025-06-06 17:53:22,372 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
2025-06-06 17:57:50,845 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
2025-06-06 17:57:53,169 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
2025-06-06 17:57:53,329 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
2025-06-06 17:57:53,508 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
2025-06-06 17:57:53,672 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
2025-06-06 17:57:53,805 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
2025-06-06 17:57:53,996 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
2025-06-06 17:57:54,506 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
2025-06-06 17:57:54,657 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
2025-06-06 17:57:54,821 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
2025-06-06 17:57:54,982 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
2025-06-06 17:58:15,952 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
2025-06-06 17:58:45,718 - WARNING - growthhive_logger - [WARNING] class-function: JWTHandler-decode_token | message: Invalid JWT token provided.
{"timestamp": "2025-06-06T12:47:16.934444", "level": "INFO", "message": "Application startup", "module": "main", "function": "startup_event", "line": 219}
{"timestamp": "2025-06-06T12:47:23.584711", "level": "INFO", "message": "Request started: GET /docs", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-06T12:47:23.585930", "level": "INFO", "message": "Request completed: GET /docs - Status: 200", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-06T12:47:23.969655", "level": "INFO", "message": "Request started: GET /openapi.json", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-06T12:47:23.996607", "level": "INFO", "message": "Request completed: GET /openapi.json - Status: 200", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-06T12:47:29.569547", "level": "INFO", "message": "Request started: POST /api/v1/auth/login", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-06T12:47:29.678121", "level": "INFO", "message": "Request completed: POST /api/v1/auth/login - Status: 401", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-06T12:47:39.054100", "level": "INFO", "message": "Request started: GET /api/v1/auth/profile", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-06T12:47:39.056969", "level": "INFO", "message": "Request completed: GET /api/v1/auth/profile - Status: 403", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-06T12:47:48.007965", "level": "INFO", "message": "Request started: GET /api/v1/auth/profile", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-06T12:47:48.036294", "level": "ERROR", "message": "Failed to decode token", "module": "jwt_handler", "function": "decode_token", "line": 116, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jws.py\", line 176, in _load\n    signing_input, crypto_segment = jwt.rsplit(b\".\", 1)\nValueError: not enough values to unpack (expected 2, got 1)\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jwt.py\", line 142, in decode\n    payload = jws.verify(token, key, algorithms, verify=verify_signature)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jws.py\", line 70, in verify\n    header, payload, signing_input, signature = _load(token)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jws.py\", line 180, in _load\n    raise JWSError(\"Not enough segments\")\njose.exceptions.JWSError: Not enough segments\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/utils/authentication_manager/jwt_handler.py\", line 102, in decode_token\n    payload = jwt.decode(\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jwt.py\", line 144, in decode\n    raise JWTError(e)\njose.exceptions.JWTError: Not enough segments"}
{"timestamp": "2025-06-06T12:47:48.037090", "level": "INFO", "message": "Request completed: GET /api/v1/auth/profile - Status: 401", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-06T12:48:04.440118", "level": "INFO", "message": "Request started: GET /api/v1/auth/profile", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-06T12:48:04.441858", "level": "INFO", "message": "Request completed: GET /api/v1/auth/profile - Status: 403", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-06T12:48:25.689579", "level": "INFO", "message": "Request started: POST /api/v1/auth/login", "module": "logging_middleware", "function": "dispatch", "line": 14}
2025-06-06 18:18:26,007 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-verify_password | message: Password verification completed: True
2025-06-06 18:18:26,007 - ERROR - growthhive_logger - [EXCEPTION] message_key: login_failed  
 class-function: AuthOperations-login_user 
 exception: 'JWTHandler' object has no attribute 'create_access_token'
Traceback (most recent call last):
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/operations/auth/auth_operations.py", line 195, in login_user
    access_token = await self.jwt_handler.create_access_token(
AttributeError: 'JWTHandler' object has no attribute 'create_access_token'
{"timestamp": "2025-06-06T12:48:26.009985", "level": "INFO", "message": "Request completed: POST /api/v1/auth/login - Status: 500", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-06T12:50:16.388864", "level": "INFO", "message": "Request started: POST /auth/login", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-06T12:50:16.391392", "level": "INFO", "message": "Request completed: POST /auth/login - Status: 404", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-06T12:51:38.964613", "level": "INFO", "message": "Request started: POST /api/v1/auth/login", "module": "logging_middleware", "function": "dispatch", "line": 14}
2025-06-06 18:21:39,253 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-verify_password | message: Password verification completed: True
2025-06-06 18:21:39,253 - ERROR - growthhive_logger - [EXCEPTION] message_key: login_failed  
 class-function: AuthOperations-login_user 
 exception: 'JWTHandler' object has no attribute 'create_access_token'
Traceback (most recent call last):
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/operations/auth/auth_operations.py", line 195, in login_user
    access_token = await self.jwt_handler.create_access_token(
AttributeError: 'JWTHandler' object has no attribute 'create_access_token'
{"timestamp": "2025-06-06T12:51:39.254624", "level": "INFO", "message": "Request completed: POST /api/v1/auth/login - Status: 500", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-06T12:52:05.644891", "level": "INFO", "message": "Application shutdown", "module": "main", "function": "shutdown_event", "line": 224}
{"timestamp": "2025-06-06T12:52:06.513447", "level": "INFO", "message": "Application startup", "module": "main", "function": "startup_event", "line": 219}
{"timestamp": "2025-06-06T12:52:09.421229", "level": "INFO", "message": "Request started: POST /api/v1/auth/login", "module": "logging_middleware", "function": "dispatch", "line": 14}
2025-06-06 18:22:09,764 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-verify_password | message: Password verification completed: True
{"timestamp": "2025-06-06T12:52:09.774855", "level": "INFO", "message": "Created access token", "module": "jwt_handler", "function": "create_access_token", "line": 65}
2025-06-06 18:22:09,774 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-login_user | message: User logged in successfully: <EMAIL>
{"timestamp": "2025-06-06T12:52:09.775315", "level": "INFO", "message": "Request completed: POST /api/v1/auth/login - Status: 200", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-06T12:52:22.647973", "level": "INFO", "message": "Request started: POST /api/v1/auth/login", "module": "logging_middleware", "function": "dispatch", "line": 14}
2025-06-06 18:22:22,933 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-verify_password | message: Password verification completed: True
{"timestamp": "2025-06-06T12:52:22.934533", "level": "INFO", "message": "Created access token", "module": "jwt_handler", "function": "create_access_token", "line": 65}
2025-06-06 18:22:22,934 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-login_user | message: User logged in successfully: <EMAIL>
{"timestamp": "2025-06-06T12:52:22.935108", "level": "INFO", "message": "Request completed: POST /api/v1/auth/login - Status: 200", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-06T12:52:26.831860", "level": "INFO", "message": "Request started: POST /api/v1/auth/login", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-06T12:52:26.843861", "level": "INFO", "message": "Request completed: POST /api/v1/auth/login - Status: 401", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-06T12:52:31.790321", "level": "INFO", "message": "Request started: GET /api/v1/auth/profile", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-06T12:52:31.791478", "level": "INFO", "message": "Request completed: GET /api/v1/auth/profile - Status: 403", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-06T12:52:36.770966", "level": "INFO", "message": "Request started: GET /api/v1/auth/profile", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-06T12:52:36.793777", "level": "ERROR", "message": "Failed to decode token", "module": "jwt_handler", "function": "decode_token", "line": 116, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jws.py\", line 176, in _load\n    signing_input, crypto_segment = jwt.rsplit(b\".\", 1)\nValueError: not enough values to unpack (expected 2, got 1)\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jwt.py\", line 142, in decode\n    payload = jws.verify(token, key, algorithms, verify=verify_signature)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jws.py\", line 70, in verify\n    header, payload, signing_input, signature = _load(token)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jws.py\", line 180, in _load\n    raise JWSError(\"Not enough segments\")\njose.exceptions.JWSError: Not enough segments\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/utils/authentication_manager/jwt_handler.py\", line 102, in decode_token\n    payload = jwt.decode(\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jwt.py\", line 144, in decode\n    raise JWTError(e)\njose.exceptions.JWTError: Not enough segments"}
{"timestamp": "2025-06-06T12:52:36.795664", "level": "INFO", "message": "Request completed: GET /api/v1/auth/profile - Status: 401", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-06T12:52:50.615112", "level": "INFO", "message": "Request started: POST /api/v1/auth/register", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-06T12:52:50.641292", "level": "INFO", "message": "Request completed: POST /api/v1/auth/register - Status: 422", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-06T12:52:59.541680", "level": "INFO", "message": "Request started: POST /api/v1/auth/register", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-06T12:52:59.553616", "level": "INFO", "message": "Request completed: POST /api/v1/auth/register - Status: 422", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-06T12:53:06.715956", "level": "INFO", "message": "Request started: POST /api/v1/auth/register", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-06T12:53:06.721922", "level": "INFO", "message": "Request completed: POST /api/v1/auth/register - Status: 422", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-06T12:53:20.027540", "level": "INFO", "message": "Request started: POST /api/v1/auth/register", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-06T12:53:20.055851", "level": "INFO", "message": "Request completed: POST /api/v1/auth/register - Status: 400", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-06T12:53:31.061739", "level": "INFO", "message": "Request started: POST /api/v1/auth/register", "module": "logging_middleware", "function": "dispatch", "line": 14}
2025-06-06 18:23:31,333 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-hash_password | message: Password hashed successfully
2025-06-06 18:23:31,345 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-register_user | message: User registered successfully: <EMAIL>
{"timestamp": "2025-06-06T12:53:31.345573", "level": "INFO", "message": "Request completed: POST /api/v1/auth/register - Status: 201", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-06T12:56:29.947990", "level": "INFO", "message": "Request started: POST /api/v1/auth/login", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-06T12:56:29.971544", "level": "INFO", "message": "Request completed: POST /api/v1/auth/login - Status: 401", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-06T12:56:45.830240", "level": "INFO", "message": "Request started: POST /api/v1/auth/login", "module": "logging_middleware", "function": "dispatch", "line": 14}
2025-06-06 18:26:46,119 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-verify_password | message: Password verification completed: True
{"timestamp": "2025-06-06T12:56:46.119836", "level": "INFO", "message": "Created access token", "module": "jwt_handler", "function": "create_access_token", "line": 65}
2025-06-06 18:26:46,120 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-login_user | message: User logged in successfully: <EMAIL>
{"timestamp": "2025-06-06T12:56:46.120293", "level": "INFO", "message": "Request completed: POST /api/v1/auth/login - Status: 200", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-06T12:58:07.143379", "level": "INFO", "message": "Application startup", "module": "main", "function": "startup_event", "line": 219}
{"timestamp": "2025-06-06T13:00:12.858224", "level": "INFO", "message": "Application startup", "module": "main", "function": "startup_event", "line": 219}
{"timestamp": "2025-06-06T13:00:19.729386", "level": "INFO", "message": "Application shutdown", "module": "main", "function": "shutdown_event", "line": 224}
{"timestamp": "2025-06-09T11:18:33.119378", "level": "INFO", "message": "Application startup", "module": "main", "function": "startup_event", "line": 219}
{"timestamp": "2025-06-09T11:18:57.051738", "level": "INFO", "message": "Request started: GET /docs", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-09T11:18:57.057595", "level": "INFO", "message": "Request completed: GET /docs - Status: 200", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T11:18:57.134130", "level": "INFO", "message": "Request started: GET /openapi.json", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-09T11:18:57.157827", "level": "INFO", "message": "Request completed: GET /openapi.json - Status: 200", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T11:21:15.369644", "level": "INFO", "message": "Application startup", "module": "main", "function": "startup_event", "line": 219}
{"timestamp": "2025-06-09T11:21:21.104715", "level": "INFO", "message": "Request started: GET /docs", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-09T11:21:21.105579", "level": "INFO", "message": "Request completed: GET /docs - Status: 200", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T11:21:21.162794", "level": "INFO", "message": "Request started: GET /openapi.json", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-09T11:21:21.191557", "level": "INFO", "message": "Request completed: GET /openapi.json - Status: 200", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T11:27:55.611585", "level": "INFO", "message": "Application startup", "module": "main", "function": "startup_event", "line": 219}
{"timestamp": "2025-06-09T11:28:42.533011", "level": "INFO", "message": "Request started: POST /api/v1/auth/register", "module": "logging_middleware", "function": "dispatch", "line": 14}
2025-06-09 16:58:42,616 - ERROR - growthhive_logger - [EXCEPTION] message_key: user_creation_failed  
 class-function: AuthOperations-register_user 
 exception: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedTableError'>: relation "users" does not exist
[SQL: SELECT users.id, users.email, users.mobile_number, users.password_hash, users.first_name, users.last_name, users.role, users.is_active, users.remember_me_token, users.password_reset_token, users.password_reset_expires, users.email_verified_at, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.email = $1::VARCHAR]
[parameters: ('<EMAIL>',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
Traceback (most recent call last):
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 533, in _prepare_and_execute
    prepared_stmt, attributes = await adapt_connection._prepare(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 784, in _prepare
    prepared_stmt = await self._connection.prepare(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/asyncpg/connection.py", line 636, in prepare
    return await self._prepare(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/asyncpg/connection.py", line 654, in _prepare
    stmt = await self._get_statement(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/asyncpg/connection.py", line 433, in _get_statement
    statement = await self._protocol.prepare(
  File "asyncpg/protocol/protocol.pyx", line 166, in prepare
asyncpg.exceptions.UndefinedTableError: relation "users" does not exist

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/engine/base.py", line 1969, in _exec_single_context
    self.dialect.do_execute(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/engine/default.py", line 922, in do_execute
    cursor.execute(statement, parameters)
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 591, in execute
    self._adapt_connection.await_(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 125, in await_only
    return current.driver.switch(awaitable)  # type: ignore[no-any-return]
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 185, in greenlet_spawn
    value = await result
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 569, in _prepare_and_execute
    self._handle_exception(error)
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 520, in _handle_exception
    self._adapt_connection._handle_exception(error)
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 808, in _handle_exception
    raise translated_error from error
sqlalchemy.dialects.postgresql.asyncpg.AsyncAdapt_asyncpg_dbapi.ProgrammingError: <class 'asyncpg.exceptions.UndefinedTableError'>: relation "users" does not exist

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/operations/auth/auth_operations.py", line 58, in register_user
    email_check = await db.execute(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/ext/asyncio/session.py", line 455, in execute
    result = await greenlet_spawn(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 190, in greenlet_spawn
    result = context.throw(*sys.exc_info())
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/orm/session.py", line 2308, in execute
    return self._execute_internal(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/orm/session.py", line 2190, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/orm/context.py", line 293, in orm_execute_statement
    result = conn.execute(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/engine/base.py", line 1416, in execute
    return meth(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/sql/elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/engine/base.py", line 1639, in _execute_clauseelement
    ret = self._execute_context(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/engine/base.py", line 1848, in _execute_context
    return self._exec_single_context(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/engine/base.py", line 1988, in _exec_single_context
    self._handle_dbapi_exception(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/engine/base.py", line 2343, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/engine/base.py", line 1969, in _exec_single_context
    self.dialect.do_execute(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/engine/default.py", line 922, in do_execute
    cursor.execute(statement, parameters)
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 591, in execute
    self._adapt_connection.await_(
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 125, in await_only
    return current.driver.switch(awaitable)  # type: ignore[no-any-return]
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 185, in greenlet_spawn
    value = await result
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 569, in _prepare_and_execute
    self._handle_exception(error)
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 520, in _handle_exception
    self._adapt_connection._handle_exception(error)
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 808, in _handle_exception
    raise translated_error from error
sqlalchemy.exc.ProgrammingError: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedTableError'>: relation "users" does not exist
[SQL: SELECT users.id, users.email, users.mobile_number, users.password_hash, users.first_name, users.last_name, users.role, users.is_active, users.remember_me_token, users.password_reset_token, users.password_reset_expires, users.email_verified_at, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.email = $1::VARCHAR]
[parameters: ('<EMAIL>',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
{"timestamp": "2025-06-09T11:28:42.635686", "level": "INFO", "message": "Request completed: POST /api/v1/auth/register - Status: 500", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T11:31:04.999518", "level": "INFO", "message": "Application startup", "module": "main", "function": "startup_event", "line": 219}
{"timestamp": "2025-06-09T11:31:11.594229", "level": "INFO", "message": "Request started: POST /api/v1/auth/register", "module": "logging_middleware", "function": "dispatch", "line": 14}
2025-06-09 17:01:11,920 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-hash_password | message: Password hashed successfully
2025-06-09 17:01:11,929 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-register_user | message: User registered successfully: <EMAIL>
{"timestamp": "2025-06-09T11:31:11.929613", "level": "INFO", "message": "Request completed: POST /api/v1/auth/register - Status: 201", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T11:32:21.567496", "level": "INFO", "message": "Request started: POST /api/v1/auth/login", "module": "logging_middleware", "function": "dispatch", "line": 14}
2025-06-09 17:02:21,855 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-verify_password | message: Password verification completed: True
{"timestamp": "2025-06-09T11:32:21.864706", "level": "INFO", "message": "Created access token", "module": "jwt_handler", "function": "create_access_token", "line": 65}
2025-06-09 17:02:21,864 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-login_user | message: User logged in successfully: <EMAIL>
{"timestamp": "2025-06-09T11:32:21.865287", "level": "INFO", "message": "Request completed: POST /api/v1/auth/login - Status: 200", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T11:32:44.010282", "level": "INFO", "message": "Request started: GET /api/v1/auth/profile", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-09T11:32:44.014076", "level": "INFO", "message": "Successfully decoded token", "module": "jwt_handler", "function": "decode_token", "line": 108}
2025-06-09 17:02:44,014 - ERROR - growthhive_logger - [EXCEPTION] message_key: unknown_error  
 class-function: AuthOperations-get_user_profile 
 exception: 'dict' object has no attribute 'id'
Traceback (most recent call last):
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/operations/auth/auth_operations.py", line 450, in get_user_profile
    id=str(current_user.id),
AttributeError: 'dict' object has no attribute 'id'
{"timestamp": "2025-06-09T11:32:44.028425", "level": "INFO", "message": "Request completed: GET /api/v1/auth/profile - Status: 500", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T11:34:55.934491", "level": "INFO", "message": "Application shutdown", "module": "main", "function": "shutdown_event", "line": 224}
{"timestamp": "2025-06-09T11:34:56.818252", "level": "INFO", "message": "Application startup", "module": "main", "function": "startup_event", "line": 219}
{"timestamp": "2025-06-09T11:36:14.806532", "level": "INFO", "message": "Application startup", "module": "main", "function": "startup_event", "line": 219}
{"timestamp": "2025-06-09T11:36:23.499589", "level": "INFO", "message": "Request started: GET /api/v1/auth/profile", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-09T11:36:23.510735", "level": "INFO", "message": "Successfully decoded token", "module": "jwt_handler", "function": "decode_token", "line": 108}
2025-06-09 17:06:23,510 - ERROR - growthhive_logger - [EXCEPTION] message_key: unknown_error  
 class-function: AuthOperations-get_user_profile 
 exception: 'dict' object has no attribute 'id'
Traceback (most recent call last):
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/operations/auth/auth_operations.py", line 450, in get_user_profile
    id=str(current_user.id),
AttributeError: 'dict' object has no attribute 'id'
{"timestamp": "2025-06-09T11:36:23.516141", "level": "INFO", "message": "Request completed: GET /api/v1/auth/profile - Status: 500", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T11:36:24.336920", "level": "INFO", "message": "Request started: GET /api/v1/auth/profile", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-09T11:36:24.337772", "level": "INFO", "message": "Successfully decoded token", "module": "jwt_handler", "function": "decode_token", "line": 108}
2025-06-09 17:06:24,337 - ERROR - growthhive_logger - [EXCEPTION] message_key: unknown_error  
 class-function: AuthOperations-get_user_profile 
 exception: 'dict' object has no attribute 'id'
Traceback (most recent call last):
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/operations/auth/auth_operations.py", line 450, in get_user_profile
    id=str(current_user.id),
AttributeError: 'dict' object has no attribute 'id'
{"timestamp": "2025-06-09T11:36:24.346486", "level": "INFO", "message": "Request completed: GET /api/v1/auth/profile - Status: 500", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T11:36:24.503012", "level": "INFO", "message": "Request started: GET /api/v1/auth/profile", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-09T11:36:24.503773", "level": "INFO", "message": "Successfully decoded token", "module": "jwt_handler", "function": "decode_token", "line": 108}
2025-06-09 17:06:24,503 - ERROR - growthhive_logger - [EXCEPTION] message_key: unknown_error  
 class-function: AuthOperations-get_user_profile 
 exception: 'dict' object has no attribute 'id'
Traceback (most recent call last):
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/operations/auth/auth_operations.py", line 450, in get_user_profile
    id=str(current_user.id),
AttributeError: 'dict' object has no attribute 'id'
{"timestamp": "2025-06-09T11:36:24.504681", "level": "INFO", "message": "Request completed: GET /api/v1/auth/profile - Status: 500", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T11:37:20.319921", "level": "INFO", "message": "Application startup", "module": "main", "function": "startup_event", "line": 219}
{"timestamp": "2025-06-09T11:37:25.391894", "level": "INFO", "message": "Request started: GET /api/v1/auth/profile", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-09T11:37:25.405753", "level": "INFO", "message": "Successfully decoded token", "module": "jwt_handler", "function": "decode_token", "line": 108}
2025-06-09 17:07:25,406 - ERROR - growthhive_logger - [EXCEPTION] message_key: unknown_error  
 class-function: AuthOperations-get_user_profile 
 exception: 'dict' object has no attribute 'id'
Traceback (most recent call last):
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/operations/auth/auth_operations.py", line 450, in get_user_profile
    id=str(current_user.id),
AttributeError: 'dict' object has no attribute 'id'
{"timestamp": "2025-06-09T11:37:25.411474", "level": "INFO", "message": "Request completed: GET /api/v1/auth/profile - Status: 500", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T11:39:01.511394", "level": "INFO", "message": "Request started: GET /api/v1/auth/profile", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-09T11:39:01.522842", "level": "INFO", "message": "Successfully decoded token", "module": "jwt_handler", "function": "decode_token", "line": 108}
2025-06-09 17:09:01,523 - ERROR - growthhive_logger - [EXCEPTION] message_key: unknown_error  
 class-function: AuthOperations-get_user_profile 
 exception: 'dict' object has no attribute 'id'
Traceback (most recent call last):
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/operations/auth/auth_operations.py", line 450, in get_user_profile
    id=str(current_user.id),
AttributeError: 'dict' object has no attribute 'id'
{"timestamp": "2025-06-09T11:39:01.524750", "level": "INFO", "message": "Request completed: GET /api/v1/auth/profile - Status: 500", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T11:39:29.850595", "level": "INFO", "message": "Request started: GET /docs", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-09T11:39:29.852780", "level": "INFO", "message": "Request completed: GET /docs - Status: 200", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T11:39:29.946780", "level": "INFO", "message": "Request started: GET /openapi.json", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-09T11:39:29.978132", "level": "INFO", "message": "Request completed: GET /openapi.json - Status: 200", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T11:39:41.268144", "level": "INFO", "message": "Request started: GET /api/v1/auth/profile", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-09T11:39:41.269874", "level": "INFO", "message": "Successfully decoded token", "module": "jwt_handler", "function": "decode_token", "line": 108}
2025-06-09 17:09:41,270 - ERROR - growthhive_logger - [EXCEPTION] message_key: unknown_error  
 class-function: AuthOperations-get_user_profile 
 exception: 'dict' object has no attribute 'id'
Traceback (most recent call last):
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/operations/auth/auth_operations.py", line 450, in get_user_profile
    id=str(current_user.id),
AttributeError: 'dict' object has no attribute 'id'
{"timestamp": "2025-06-09T11:39:41.271299", "level": "INFO", "message": "Request completed: GET /api/v1/auth/profile - Status: 500", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T11:44:33.067656", "level": "INFO", "message": "Request started: POST /api/v1/auth/register", "module": "logging_middleware", "function": "dispatch", "line": 14}
2025-06-09 17:14:33,454 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-hash_password | message: Password hashed successfully
2025-06-09 17:14:33,462 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-register_user | message: User registered successfully: <EMAIL>
{"timestamp": "2025-06-09T11:44:33.462772", "level": "INFO", "message": "Request completed: POST /api/v1/auth/register - Status: 201", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T11:44:59.512770", "level": "INFO", "message": "Request started: POST /api/v1/auth/login", "module": "logging_middleware", "function": "dispatch", "line": 14}
2025-06-09 17:14:59,790 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-verify_password | message: Password verification completed: True
{"timestamp": "2025-06-09T11:44:59.797869", "level": "INFO", "message": "Created access token", "module": "jwt_handler", "function": "create_access_token", "line": 65}
2025-06-09 17:14:59,798 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-login_user | message: User logged in successfully: <EMAIL>
{"timestamp": "2025-06-09T11:44:59.798509", "level": "INFO", "message": "Request completed: POST /api/v1/auth/login - Status: 200", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T11:45:36.427432", "level": "INFO", "message": "Request started: GET /api/v1/auth/profile", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-09T11:45:36.431365", "level": "INFO", "message": "Successfully decoded token", "module": "jwt_handler", "function": "decode_token", "line": 108}
2025-06-09 17:15:36,431 - ERROR - growthhive_logger - [EXCEPTION] message_key: unknown_error  
 class-function: AuthOperations-get_user_profile 
 exception: 'dict' object has no attribute 'id'
Traceback (most recent call last):
  File "/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/operations/auth/auth_operations.py", line 450, in get_user_profile
    id=str(current_user.id),
AttributeError: 'dict' object has no attribute 'id'
{"timestamp": "2025-06-09T11:45:36.433226", "level": "INFO", "message": "Request completed: GET /api/v1/auth/profile - Status: 500", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T11:56:27.250805", "level": "INFO", "message": "Application shutdown", "module": "main", "function": "shutdown_event", "line": 224}
{"timestamp": "2025-06-09T11:56:28.138433", "level": "INFO", "message": "Application startup", "module": "main", "function": "startup_event", "line": 219}
{"timestamp": "2025-06-09T11:56:31.074061", "level": "INFO", "message": "Application shutdown", "module": "main", "function": "shutdown_event", "line": 224}
{"timestamp": "2025-06-09T11:56:31.725096", "level": "INFO", "message": "Application startup", "module": "main", "function": "startup_event", "line": 219}
{"timestamp": "2025-06-09T11:56:35.970083", "level": "INFO", "message": "Application shutdown", "module": "main", "function": "shutdown_event", "line": 224}
{"timestamp": "2025-06-09T12:12:38.020992", "level": "INFO", "message": "Application startup", "module": "main", "function": "startup_event", "line": 219}
{"timestamp": "2025-06-09T12:13:09.601112", "level": "INFO", "message": "Application startup", "module": "main", "function": "startup_event", "line": 219}
{"timestamp": "2025-06-09T12:13:23.862642", "level": "INFO", "message": "Request started: GET /docs", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-09T12:13:23.863706", "level": "INFO", "message": "Request completed: GET /docs - Status: 200", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T12:13:24.231936", "level": "INFO", "message": "Request started: GET /openapi.json", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-09T12:13:24.260945", "level": "INFO", "message": "Request completed: GET /openapi.json - Status: 200", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T12:14:11.077177", "level": "INFO", "message": "Request started: POST /api/v1/auth/register", "module": "logging_middleware", "function": "dispatch", "line": 14}
2025-06-09 17:44:11,431 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-hash_password | message: Password hashed successfully
2025-06-09 17:44:11,443 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-register_user | message: User registered successfully: <EMAIL>
{"timestamp": "2025-06-09T12:14:11.444024", "level": "INFO", "message": "Request completed: POST /api/v1/auth/register - Status: 201", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T12:14:49.567936", "level": "INFO", "message": "Request started: POST /api/v1/auth/login", "module": "logging_middleware", "function": "dispatch", "line": 14}
2025-06-09 17:44:49,849 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-verify_password | message: Password verification completed: True
{"timestamp": "2025-06-09T12:14:49.858117", "level": "INFO", "message": "Created access token", "module": "jwt_handler", "function": "create_access_token", "line": 65}
2025-06-09 17:44:49,858 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-login_user | message: User logged in successfully: <EMAIL>
{"timestamp": "2025-06-09T12:14:49.858546", "level": "INFO", "message": "Request completed: POST /api/v1/auth/login - Status: 200", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T12:37:23.783559", "level": "INFO", "message": "Application shutdown", "module": "main", "function": "shutdown_event", "line": 224}
{"timestamp": "2025-06-09T12:37:24.638526", "level": "INFO", "message": "Application startup", "module": "main", "function": "startup_event", "line": 219}
{"timestamp": "2025-06-09T12:38:47.958002", "level": "INFO", "message": "Application shutdown", "module": "main", "function": "shutdown_event", "line": 224}
{"timestamp": "2025-06-09T12:38:48.733926", "level": "INFO", "message": "Application startup", "module": "main", "function": "startup_event", "line": 219}
{"timestamp": "2025-06-09T12:40:55.910385", "level": "INFO", "message": "Application startup", "module": "main", "function": "startup_event", "line": 219}
{"timestamp": "2025-06-09T12:40:59.710842", "level": "INFO", "message": "Request started: POST /api/v1/auth/login", "module": "logging_middleware", "function": "dispatch", "line": 14}
2025-06-09 18:11:00,052 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-verify_password | message: Password verification completed: True
{"timestamp": "2025-06-09T12:41:00.065813", "level": "INFO", "message": "Created access token", "module": "jwt_handler", "function": "create_access_token", "line": 65}
2025-06-09 18:11:00,066 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-login_user | message: User logged in successfully: <EMAIL>
{"timestamp": "2025-06-09T12:41:00.066604", "level": "INFO", "message": "Request completed: POST /api/v1/auth/login - Status: 200", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T12:41:52.792344", "level": "INFO", "message": "Request started: POST /api/v1/auth/logout", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-09T12:41:52.804322", "level": "ERROR", "message": "Failed to decode token", "module": "jwt_handler", "function": "decode_token", "line": 116, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jws.py\", line 176, in _load\n    signing_input, crypto_segment = jwt.rsplit(b\".\", 1)\nValueError: not enough values to unpack (expected 2, got 1)\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jwt.py\", line 142, in decode\n    payload = jws.verify(token, key, algorithms, verify=verify_signature)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jws.py\", line 70, in verify\n    header, payload, signing_input, signature = _load(token)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jws.py\", line 180, in _load\n    raise JWSError(\"Not enough segments\")\njose.exceptions.JWSError: Not enough segments\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/utils/authentication_manager/jwt_handler.py\", line 102, in decode_token\n    payload = jwt.decode(\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jwt.py\", line 144, in decode\n    raise JWTError(e)\njose.exceptions.JWTError: Not enough segments"}
{"timestamp": "2025-06-09T12:41:52.806310", "level": "INFO", "message": "Request completed: POST /api/v1/auth/logout - Status: 401", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T12:46:05.129002", "level": "INFO", "message": "Request started: POST /api/v1/auth/logout", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-09T12:46:05.137531", "level": "INFO", "message": "Successfully decoded token", "module": "jwt_handler", "function": "decode_token", "line": 108}
{"timestamp": "2025-06-09T12:46:05.165209", "level": "ERROR", "message": "Request failed: POST /api/v1/auth/logout", "module": "logging_middleware", "function": "dispatch", "line": 33, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/base.py\", line 78, in call_next\n    message = await recv_stream.receive()\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/anyio/streams/memory.py\", line 98, in receive\n    return self.receive_nowait()\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/anyio/streams/memory.py\", line 91, in receive_nowait\n    raise EndOfStream\nanyio.EndOfStream\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/middleware/logging_middleware.py\", line 21, in dispatch\n    response = await call_next(request)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/base.py\", line 84, in call_next\n    raise app_exc\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/base.py\", line 70, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n    await self.simple_response(scope, receive, send, request_headers=headers)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n    raise exc\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n    await self.app(scope, receive, sender)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n    raise e\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/routing.py\", line 718, in __call__\n    await route.handle(scope, receive, send)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/routing.py\", line 276, in handle\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/routing.py\", line 66, in app\n    response = await func(request)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/fastapi/routing.py\", line 274, in app\n    raw_response = await run_endpoint_function(\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/fastapi/routing.py\", line 191, in run_endpoint_function\n    return await dependant.call(**values)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/api/v1/auth.py\", line 98, in logout_user\n    return await auth_operations.logout_user(str(current_user.id), language)\nAttributeError: 'dict' object has no attribute 'id'"}
{"timestamp": "2025-06-09T12:56:58.500334", "level": "INFO", "message": "Request started: GET /api/v1/auth/validate-token", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-09T12:56:58.509351", "level": "ERROR", "message": "Failed to decode token", "module": "jwt_handler", "function": "decode_token", "line": 116, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jws.py\", line 176, in _load\n    signing_input, crypto_segment = jwt.rsplit(b\".\", 1)\nValueError: not enough values to unpack (expected 2, got 1)\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jwt.py\", line 142, in decode\n    payload = jws.verify(token, key, algorithms, verify=verify_signature)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jws.py\", line 70, in verify\n    header, payload, signing_input, signature = _load(token)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jws.py\", line 180, in _load\n    raise JWSError(\"Not enough segments\")\njose.exceptions.JWSError: Not enough segments\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/utils/authentication_manager/jwt_handler.py\", line 102, in decode_token\n    payload = jwt.decode(\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jwt.py\", line 144, in decode\n    raise JWTError(e)\njose.exceptions.JWTError: Not enough segments"}
{"timestamp": "2025-06-09T12:56:58.514305", "level": "INFO", "message": "Request completed: GET /api/v1/auth/validate-token - Status: 401", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T13:09:27.720399", "level": "INFO", "message": "Application startup", "module": "main", "function": "startup_event", "line": 219}
{"timestamp": "2025-06-09T13:09:40.460957", "level": "INFO", "message": "Request started: GET /docs", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-09T13:09:40.462916", "level": "INFO", "message": "Request completed: GET /docs - Status: 200", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T13:09:40.571968", "level": "INFO", "message": "Request started: GET /openapi.json", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-09T13:09:40.599586", "level": "INFO", "message": "Request completed: GET /openapi.json - Status: 200", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T13:10:03.806969", "level": "INFO", "message": "Request started: POST /api/v1/auth/login", "module": "logging_middleware", "function": "dispatch", "line": 14}
2025-06-09 18:40:04,162 - INFO - growthhive_logger - [INFO] class-function: PasswordHandler-verify_password | message: Password verification completed: True
{"timestamp": "2025-06-09T13:10:04.179903", "level": "INFO", "message": "Created access token", "module": "jwt_handler", "function": "create_access_token", "line": 65}
2025-06-09 18:40:04,180 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-login_user | message: User logged in successfully: <EMAIL>
{"timestamp": "2025-06-09T13:10:04.180307", "level": "INFO", "message": "Request completed: POST /api/v1/auth/login - Status: 200", "module": "logging_middleware", "function": "dispatch", "line": 24}
{"timestamp": "2025-06-09T13:10:21.116694", "level": "INFO", "message": "Request started: POST /api/v1/auth/logout", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-09T13:10:21.118304", "level": "INFO", "message": "Successfully decoded token", "module": "jwt_handler", "function": "decode_token", "line": 108}
{"timestamp": "2025-06-09T13:10:21.139628", "level": "ERROR", "message": "Request failed: POST /api/v1/auth/logout", "module": "logging_middleware", "function": "dispatch", "line": 33, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/base.py\", line 78, in call_next\n    message = await recv_stream.receive()\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/anyio/streams/memory.py\", line 98, in receive\n    return self.receive_nowait()\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/anyio/streams/memory.py\", line 91, in receive_nowait\n    raise EndOfStream\nanyio.EndOfStream\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/middleware/logging_middleware.py\", line 21, in dispatch\n    response = await call_next(request)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/base.py\", line 84, in call_next\n    raise app_exc\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/base.py\", line 70, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n    await self.simple_response(scope, receive, send, request_headers=headers)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n    raise exc\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n    await self.app(scope, receive, sender)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n    raise e\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/routing.py\", line 718, in __call__\n    await route.handle(scope, receive, send)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/routing.py\", line 276, in handle\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/routing.py\", line 66, in app\n    response = await func(request)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/fastapi/routing.py\", line 274, in app\n    raw_response = await run_endpoint_function(\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/fastapi/routing.py\", line 191, in run_endpoint_function\n    return await dependant.call(**values)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/api/v1/auth.py\", line 98, in logout_user\n    return await auth_operations.logout_user(str(current_user.id), language)\nAttributeError: 'dict' object has no attribute 'id'"}
{"timestamp": "2025-06-09T13:12:49.177486", "level": "INFO", "message": "Application shutdown", "module": "main", "function": "shutdown_event", "line": 224}
{"timestamp": "2025-06-09T13:12:49.920342", "level": "INFO", "message": "Application startup", "module": "main", "function": "startup_event", "line": 219}
{"timestamp": "2025-06-09T13:13:32.341036", "level": "INFO", "message": "Request started: POST /api/v1/auth/logout", "module": "logging_middleware", "function": "dispatch", "line": 14}
{"timestamp": "2025-06-09T13:13:32.347907", "level": "INFO", "message": "Successfully decoded token", "module": "jwt_handler", "function": "decode_token", "line": 108}
2025-06-09 18:43:32,348 - INFO - growthhive_logger - [INFO] class-function: AuthOperations-logout_user | message: User logged out: d107b4bb-b3f5-4f1e-99f6-263b84facaf7
{"timestamp": "2025-06-09T13:13:32.348841", "level": "INFO", "message": "Request completed: POST /api/v1/auth/logout - Status: 200", "module": "logging_middleware", "function": "dispatch", "line": 24}
