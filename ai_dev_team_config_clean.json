{"provider": "autogen_agentchat.teams.SelectorGroupChat", "component_type": "team", "version": 1, "component_version": 1, "description": "A comprehensive team of 8 specialized agents for building Python AI applications with FastAPI, RAG, vector analysis, SMS integration, and Supabase backend - including architecture design, development, testing, and deployment specialists.", "label": "AI_Application_Development_Team", "config": {"participants": [{"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "label": "AugmentPlanner", "description": "Simulated Augment-style task planner", "config": {"name": "augment_planner", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"model": "gpt-4o", "temperature": 0.3, "api_key": "********************************************************************************************************************************************************************"}}, "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of all messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "Augment-style Task Planner and Coordinator", "system_message": "You are a senior planner simulating Augment AI behavior. Break large tasks into atomic subtasks, prioritize them, and delegate to appropriate agents in the team. Assign tasks like fastapi_developer, vector_expert, sms_integration_specialist, etc. Be precise, efficient, and make sure every subgoal is realistic and self-contained.", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}", "metadata": {}}}, {"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "Lead architect specializing in AI application design and system architecture", "label": "AI_Architect", "config": {"name": "ai_architect", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"temperature": 0.3, "model": "gpt-4o", "api_key": "********************************************************************************************************************************************************************"}}, "workbench": {"provider": "autogen_core.tools.StaticWorkbench", "component_type": "workbench", "version": 1, "component_version": 1, "description": "A workbench that provides a static set of tools that do not change after each tool execution.", "label": "StaticWorkbench", "config": {"tools": [{"provider": "autogen_core.tools.FunctionTool", "component_type": "tool", "version": 1, "component_version": 1, "description": "Create architecture diagrams and system designs", "label": "ArchitectureTool", "config": {"source_code": "async def create_architecture_diagram(components: List[str], connections: List[Dict[str, str]], output_format: str = 'mermaid') -> str:\n    \"\"\"Create system architecture diagrams\n    \n    Args:\n        components: List of system components\n        connections: List of connections between components\n        output_format: Output format (mermaid, plantuml)\n    \n    Returns:\n        str: Architecture diagram in specified format\n    \"\"\"\n    if output_format == 'mermaid':\n        diagram = 'graph TD\\n'\n        for component in components:\n            diagram += f'    {component.replace(\" \", \"_\")}[\"{component}\"]\\n'\n        for conn in connections:\n            source = conn['from'].replace(' ', '_')\n            target = conn['to'].replace(' ', '_')\n            label = conn.get('label', '')\n            diagram += f'    {source} --> {target}'\n            if label:\n                diagram += f' : {label}'\n            diagram += '\\n'\n        return diagram\n    return 'Architecture diagram created'", "name": "create_architecture_diagram", "description": "Create system architecture diagrams for AI applications", "global_imports": [{"module": "typing", "imports": ["List", "Dict"]}], "has_cancellation_support": false}}]}}, "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of all messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "AI Architecture Specialist - designs scalable AI application architectures", "system_message": "You are an AI Architecture Specialist with expertise in designing scalable AI applications. Your responsibilities include: 1. Design overall system architecture for AI applications 2. Plan integration patterns for FastAPI, RAG, vector databases, and external services 3. Define data flow and component interactions 4. Ensure scalability, security, and performance considerations 5. Create technical specifications and architecture diagrams 6. Guide technology stack decisions 7. Plan deployment and infrastructure requirements. Focus on microservices architecture, cloud-native solutions, and best practices for AI/ML applications.", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}", "metadata": {}}}], "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"temperature": 0.3, "model": "gpt-4o", "api_key": "********************************************************************************************************************************************************************"}}, "termination_condition": {"provider": "autogen_agentchat.base.OrTerminationCondition", "component_type": "termination", "version": 1, "component_version": 1, "label": "OrTerminationCondition", "config": {"conditions": [{"provider": "autogen_agentchat.conditions.TextMentionTermination", "component_type": "termination", "version": 1, "component_version": 1, "description": "Terminate the conversation if a specific text is mentioned.", "label": "TextMentionTermination", "config": {"text": "TERMINATE"}}, {"provider": "autogen_agentchat.conditions.MaxMessageTermination", "component_type": "termination", "version": 1, "component_version": 1, "description": "Terminate the conversation after a maximum number of messages have been exchanged.", "label": "MaxMessageTermination", "config": {"max_messages": 20, "include_agent_event": false}}]}}, "selector_prompt": "Always begin with augment_planner to decompose tasks. You are coordinating an AI application development team by selecting the team member to speak/act next. The following specialized roles are available: {roles}. Based on the conversation context and current development needs, select the most appropriate specialist. {history}. Read the above conversation. Then select the next role from {participants} to play. ONLY RETURN THE ROLE.", "allow_repeated_speaker": true, "max_selector_attempts": 3, "emit_team_events": false, "model_client_streaming": false}}