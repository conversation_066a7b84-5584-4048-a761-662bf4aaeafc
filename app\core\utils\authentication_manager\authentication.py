from typing import Optional, Dict, Any, Union
import uuid
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.core.database_management import get_db
from app.core.models.sql_alchemy_models.user_models import User
from app.core.utils.authentication_manager.jwt_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from app.core.utils.exception_manager.custom_exceptions import (
    AuthenticationError,
    ResourceNotFoundError
)
from app.core.utils.exception_manager.exception_handler import exception_handler_obj
from app.core.models.pydantic_models.response_model import MessageModel

# Security scheme for Bearer token
security = HTTPBearer()


class AuthenticationManager:
    """Authentication manager for handling user authentication"""
    
    def __init__(self):
        """Initialize authentication manager"""
        self.jwt_handler = J<PERSON><PERSON>andler()
        self.security = HTTPBearer()

    async def get_current_user(
        self,
        credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())
    ) -> Dict[str, Any]:
        """
        Get current authenticated user from JWT token
        
        Args:
            credentials: HTTP authorization credentials
            
        Returns:
            Dict[str, Any]: User data from token
            
        Raises:
            AuthenticationError: If token is invalid or expired
        """
        try:
            # Get token from credentials
            token = credentials.credentials
            
            # Decode token
            payload = await self.jwt_handler.decode_token(token)
            
            # Verify token type
            if payload.get("type") != "access":
                raise AuthenticationError(
                    "invalid_token_type",
                    message=MessageModel(
                        title="Invalid Token",
                        description="Invalid token type. Please login again."
                    )
                )
                
            # Get user data
            user_id = payload.get("user_id")
            email = payload.get("email")
            
            if not user_id or not email:
                raise AuthenticationError(
                    "invalid_token_payload",
                    message=MessageModel(
                        title="Invalid Token",
                        description="Token payload is invalid. Please login again."
                    )
                )
                
            return {
                "user_id": user_id,
                "email": email
            }
            
        except AuthenticationError as e:
            # Re-raise authentication errors
            raise e
            
        except Exception as e:
            # Handle unexpected errors
            raise AuthenticationError(
                "authentication_failed",
                message=MessageModel(
                    title="Authentication Failed",
                    description="Failed to authenticate user. Please try again."
                ),
                details={"error": str(e)}
            )
            
    async def create_access_token(
        self,
        user_data: Dict[str, Any],
        remember_me: bool = True
    ) -> str:
        """
        Create access token for user
        
        Args:
            user_data: User data to include in token
            remember_me: Whether to create a long-lived token
            
        Returns:
            str: JWT access token
        """
        try:
            # Create token payload
            payload = {
                "user_id": str(user_data.get("user_id")),
                "email": user_data.get("email")
            }
            
            # Create token
            return await self.jwt_handler.create_token(
                data=payload,
                token_type="access",
                remember_me=remember_me
            )
            
        except Exception as e:
            raise AuthenticationError(
                "token_creation_failed",
                message=MessageModel(
                    title="Token Creation Failed",
                    description="Failed to create authentication token. Please try again."
                ),
                details={"error": str(e)}
            )
            
    async def create_refresh_token(
        self,
        user_data: Dict[str, Any]
    ) -> str:
        """
        Create refresh token for user
        
        Args:
            user_data: User data to include in token
            
        Returns:
            str: JWT refresh token
        """
        try:
            # Create token payload
            payload = {
                "user_id": str(user_data.get("user_id")),
                "email": user_data.get("email")
            }
            
            # Create token
            return await self.jwt_handler.create_token(
                data=payload,
                token_type="refresh",
                remember_me=True  # Refresh tokens are always long-lived
            )
            
        except Exception as e:
            raise AuthenticationError(
                "token_creation_failed",
                message=MessageModel(
                    title="Token Creation Failed",
                    description="Failed to create refresh token. Please try again."
                ),
                details={"error": str(e)}
            )
            
    async def verify_token(self, token: str) -> bool:
        """
        Verify JWT token without raising exceptions
        
        Args:
            token: JWT token to verify
            
        Returns:
            bool: True if token is valid, False otherwise
        """
        return await self.jwt_handler.verify_token(token)

    async def get_current_active_user(
        self, 
        current_user: User = Depends(lambda: authentication_manager.get_current_user)
    ) -> User:
        """
        Get current active user (additional check for active status)
        
        Args:
            current_user: Current user from get_current_user dependency
            
        Returns:
            User: Current active user
            
        Raises:
            HTTPException: If user is inactive
        """
        if not current_user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, 
                detail="Inactive user"
            )
        return current_user


# Global instance
authentication_manager = AuthenticationManager()

# Dependencies for use in endpoints
get_current_user = authentication_manager.get_current_user
get_current_active_user = authentication_manager.get_current_active_user 