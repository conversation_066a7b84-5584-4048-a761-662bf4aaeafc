import asyncio
import os
import threading
import json
import time
from datetime import datetime
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.teams import SelectorGroupChat
from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_agentchat.ui import Console

class VerboseConsole:
    """Enhanced console that shows detailed agent communication and task updates"""

    def __init__(self, interactive_dev):
        self.interactive_dev = interactive_dev
        self.current_agent = None
        self.task_counter = 0

    async def __call__(self, stream):
        """Process the stream and display detailed information"""
        print("\n🔍 DETAILED AGENT COMMUNICATION & TASK UPDATES")
        print("=" * 80)

        async for message in stream:
            await self.process_message(message)

    async def process_message(self, message):
        """Process and display each message with detailed AutoGen-style information"""
        try:
            self.message_counter += 1

            # Extract message details
            if hasattr(message, 'source'):
                agent_name = message.source
                agent_color = self.agent_colors.get(agent_name, '⚪')

                if agent_name != self.current_agent:
                    self.current_agent = agent_name
                    self.task_counter += 1

                    # AutoGen Studio style agent switch
                    print(f"\n{'='*80}")
                    print(f"{agent_color} AGENT: {agent_name.upper().replace('_', ' ')} | Task #{self.task_counter}")
                    print(f"{'='*80}")

            # Display message content with AutoGen styling
            if hasattr(message, 'content'):
                content = message.content
                timestamp = datetime.now().strftime("%H:%M:%S")

                # Message header
                print(f"\n[{timestamp}] {agent_color} {self.current_agent or 'Agent'} (Message #{self.message_counter})")
                print("─" * 80)

                # Categorize and style the message
                if "```" in content:
                    print("💻 CODE GENERATION:")
                    print(content)
                elif "TERMINATE" in content.upper():
                    print("✅ TASK COMPLETED")
                    print(content)
                elif any(keyword in content.lower() for keyword in ['error', 'failed', 'issue', 'problem']):
                    print("⚠️  ISSUE DETECTED:")
                    print(content)
                elif any(keyword in content.lower() for keyword in ['implement', 'create', 'build', 'develop', 'write']):
                    print("🔨 IMPLEMENTATION:")
                    print(content)
                elif any(keyword in content.lower() for keyword in ['design', 'plan', 'architecture', 'structure']):
                    print("🏗️  DESIGN & PLANNING:")
                    print(content)
                elif any(keyword in content.lower() for keyword in ['test', 'verify', 'check', 'validate']):
                    print("🧪 TESTING & VALIDATION:")
                    print(content)
                elif any(keyword in content.lower() for keyword in ['integrate', 'connect', 'api', 'service']):
                    print("🔗 INTEGRATION:")
                    print(content)
                elif any(keyword in content.lower() for keyword in ['database', 'sql', 'schema', 'table']):
                    print("🗄️  DATABASE WORK:")
                    print(content)
                else:
                    print("💬 COMMUNICATION:")
                    print(content)

                print("─" * 80)

                # Store conversation history
                self.conversation_history.append({
                    "timestamp": timestamp,
                    "agent": self.current_agent,
                    "message": content,
                    "task_id": self.task_counter
                })

            # Show current suggestions if any
            suggestions = self.interactive_dev.get_suggestions_for_ai()
            if suggestions and "URGENT" in suggestions:
                print("\n🔥 URGENT USER SUGGESTIONS PENDING!")
                print("─" * 40)
                print(suggestions)
                print("─" * 40)

        except Exception as e:
            print(f"❌ Error processing message: {e}")
            print(f"Raw message: {message}")

    def display_conversation_summary(self):
        """Display a summary of the conversation"""
        print(f"\n{'='*80}")
        print("📊 CONVERSATION SUMMARY")
        print(f"{'='*80}")
        print(f"Total Messages: {self.message_counter}")
        print(f"Total Tasks: {self.task_counter}")
        print(f"Agents Participated: {len(set(msg['agent'] for msg in self.conversation_history))}")
        print("─" * 80)

        # Show agent participation
        agent_counts = {}
        for msg in self.conversation_history:
            agent = msg['agent']
            agent_counts[agent] = agent_counts.get(agent, 0) + 1

        for agent, count in agent_counts.items():
            color = self.agent_colors.get(agent, '⚪')
            print(f"{color} {agent}: {count} messages")

        print(f"{'='*80}")

class TaskTracker:
    """Track and display current development tasks"""

    def __init__(self):
        self.current_tasks = []
        self.completed_tasks = []
        self.task_phases = [
            "🏗️ System Architecture Design",
            "🗄️ Database Schema Creation",
            "🔐 Authentication System",
            "📊 Franchise Management APIs",
            "📄 Document Management System",
            "🤖 AI/RAG Implementation",
            "📱 SMS Integration",
            "🔄 Zoho CRM Integration",
            "📅 Calendar Integration",
            "📈 Analytics & Reporting",
            "🧪 Testing & Validation"
        ]
        self.current_phase_index = 0

    def get_current_phase(self):
        """Get current development phase"""
        if self.current_phase_index < len(self.task_phases):
            return self.task_phases[self.current_phase_index]
        return "🎯 Final Integration & Testing"

    def advance_phase(self):
        """Move to next development phase"""
        self.current_phase_index += 1

    def display_progress(self):
        """Display current development progress"""
        print(f"\n📊 DEVELOPMENT PROGRESS:")
        print(f"Current Phase: {self.get_current_phase()}")
        print(f"Progress: {self.current_phase_index}/{len(self.task_phases)} phases")

        # Show progress bar
        progress = min(self.current_phase_index / len(self.task_phases), 1.0)
        bar_length = 30
        filled_length = int(bar_length * progress)
        bar = "█" * filled_length + "░" * (bar_length - filled_length)
        print(f"[{bar}] {progress*100:.1f}%")
        print("-" * 50)

class InteractiveDevelopment:
    def __init__(self):
        self.user_suggestions = []
        self.development_active = True
        self.suggestion_file = "live_suggestions.json"

    def start_suggestion_listener(self):
        """Start the interactive suggestion listener in a separate thread"""
        def suggestion_loop():
            print("\n" + "="*80)
            print("🎯 INTERACTIVE DEVELOPMENT TERMINAL")
            print("="*80)
            print("💡 Provide suggestions while the AI team develops the project")
            print("📝 Your input will be incorporated in real-time")
            print("="*80)
            print("\nCommands:")
            print("  - Type your suggestion and press Enter")
            print("  - 'urgent: <suggestion>' for urgent changes")
            print("  - 'status' to see current suggestions")
            print("  - 'clear' to clear suggestions")
            print("  - 'stop' to stop development")
            print("\n" + "="*80)

            while self.development_active:
                try:
                    user_input = input("\n💬 Your suggestion: ").strip()

                    if not user_input:
                        continue

                    if user_input.lower() == 'stop':
                        self.development_active = False
                        print("🛑 Development will stop after current task...")
                        break

                    elif user_input.lower() == 'status':
                        self.show_suggestions()

                    elif user_input.lower() == 'clear':
                        self.user_suggestions.clear()
                        self.save_suggestions()
                        print("🗑️ All suggestions cleared!")

                    elif user_input.lower().startswith('urgent:'):
                        suggestion = user_input[7:].strip()
                        if suggestion:
                            self.add_suggestion(suggestion, priority="urgent")
                            print("🔥 URGENT suggestion added! AI team will implement immediately.")

                    else:
                        self.add_suggestion(user_input)
                        print("✅ Suggestion added! AI team will incorporate this.")

                except KeyboardInterrupt:
                    self.development_active = False
                    print("\n🛑 Development stopped by user.")
                    break
                except Exception as e:
                    print(f"❌ Error: {e}")

        # Start suggestion listener in a separate thread
        suggestion_thread = threading.Thread(target=suggestion_loop, daemon=True)
        suggestion_thread.start()
        return suggestion_thread

    def add_suggestion(self, suggestion, priority="normal"):
        """Add a user suggestion"""
        suggestion_data = {
            "timestamp": datetime.now().isoformat(),
            "suggestion": suggestion,
            "priority": priority,
            "implemented": False
        }
        self.user_suggestions.append(suggestion_data)
        self.save_suggestions()

    def save_suggestions(self):
        """Save suggestions to file"""
        try:
            with open(self.suggestion_file, 'w') as f:
                json.dump(self.user_suggestions, f, indent=2)
        except Exception as e:
            print(f"Error saving suggestions: {e}")

    def show_suggestions(self):
        """Show current suggestions"""
        if not self.user_suggestions:
            print("📝 No suggestions yet.")
            return

        print("\n📋 CURRENT SUGGESTIONS:")
        print("-" * 60)
        for i, suggestion in enumerate(self.user_suggestions, 1):
            status = "✅" if suggestion['implemented'] else "⏳"
            priority = "🔥" if suggestion['priority'] == 'urgent' else "📝"
            print(f"{status} {priority} {i}. {suggestion['suggestion']}")
        print("-" * 60)

    def get_suggestions_for_ai(self):
        """Get formatted suggestions for AI team"""
        pending_suggestions = [s for s in self.user_suggestions if not s['implemented']]

        if not pending_suggestions:
            return ""

        formatted = "\n\n🔥 REAL-TIME USER SUGGESTIONS:\n"
        for suggestion in pending_suggestions:
            priority_text = "🔥 URGENT" if suggestion['priority'] == 'urgent' else "📝 NORMAL"
            formatted += f"- {priority_text}: {suggestion['suggestion']}\n"

        formatted += "\nPlease incorporate these suggestions into your current development work.\n"
        return formatted

async def main():
    """Create and run the AI development team with interactive suggestions"""

    # Set up environment variables
    os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"

    print("✅ OpenAI API key configured successfully")

    # Initialize interactive development system
    interactive_dev = InteractiveDevelopment()
    task_tracker = TaskTracker()

    # Start suggestion listener in background
    print("🚀 Starting interactive suggestion system...")
    suggestion_thread = interactive_dev.start_suggestion_listener()

    # Initialize AutoGen Studio-style console for detailed communication
    autogen_console = AutoGenStudioConsole(interactive_dev)

    # Give user a moment to see the interface
    await asyncio.sleep(2)

    # Display initial progress
    task_tracker.display_progress()
    
    try:
        # Create model client
        model_client = OpenAIChatCompletionClient(
            model="gpt-4o",
            temperature=0.3
        )
        
        # Create agents
        ai_architect = AssistantAgent(
            name="ai_architect",
            model_client=model_client,
            system_message="""You are an AI Architecture Specialist with expertise in designing scalable AI applications. Your responsibilities include:
1. Design overall system architecture for AI applications
2. Plan integration patterns for FastAPI, RAG, vector databases, and external services
3. Define data flow and component interactions
4. Ensure scalability, security, and performance considerations
5. Create technical specifications and architecture diagrams
6. Guide technology stack decisions
7. Plan deployment and infrastructure requirements
Focus on microservices architecture, cloud-native solutions, and best practices for AI/ML applications."""
        )
        
        fastapi_developer = AssistantAgent(
            name="fastapi_developer",
            model_client=OpenAIChatCompletionClient(model="gpt-4o", temperature=0.2),
            system_message="""You are a FastAPI Backend Development Specialist. Your expertise includes:
1. Building high-performance FastAPI applications
2. Implementing RESTful APIs with proper documentation
3. Database integration with Supabase and PostgreSQL
4. Authentication and authorization (JWT, OAuth2)
5. Middleware implementation and request/response handling
6. API versioning and rate limiting
7. WebSocket implementation for real-time features
8. Integration with external services and APIs
9. Performance optimization and caching strategies
Focus on clean, maintainable code following FastAPI best practices."""
        )
        
        supabase_specialist = AssistantAgent(
            name="supabase_specialist",
            model_client=OpenAIChatCompletionClient(model="gpt-4o", temperature=0.2),
            system_message="""You are a Supabase and Database Specialist. Your expertise includes:
1. Supabase setup, configuration, and optimization
2. PostgreSQL database design and performance tuning
3. pgvector extension for vector storage and similarity search
4. Row Level Security (RLS) and authentication policies
5. Real-time subscriptions and database triggers
6. Supabase Auth integration and user management
7. Edge Functions and serverless database operations
8. Database migrations and schema management
9. Backup, recovery, and monitoring strategies
10. Integration with Python applications using supabase-py
Focus on scalable, secure database architectures optimized for AI applications."""
        )
        
        rag_ai_specialist = AssistantAgent(
            name="rag_ai_specialist",
            model_client=OpenAIChatCompletionClient(model="gpt-4o", temperature=0.2),
            system_message="""You are a RAG (Retrieval-Augmented Generation) and AI Integration Specialist. Your expertise includes:
1. Designing and implementing RAG pipelines for intelligent document retrieval
2. LLM integration with OpenAI, Anthropic, and other AI models
3. Embedding strategies and semantic search optimization
4. Context management and prompt engineering
5. AI model fine-tuning and optimization
6. Knowledge base construction and maintenance
7. Intelligent FAQ systems and chatbot development
8. AI-powered content generation and summarization
9. Multi-modal AI integration (text, image, audio)
10. AI model evaluation and performance monitoring
Focus on creating intelligent, context-aware AI systems that provide accurate and relevant responses."""
        )
        
        # Create termination condition
        termination = MaxMessageTermination(max_messages=50)
        
        # Create team
        team = SelectorGroupChat(
            participants=[ai_architect, fastapi_developer, supabase_specialist, rag_ai_specialist],
            model_client=model_client,
            termination_condition=termination
        )
        
        # Define the base task
        base_task = """
        Build an AI Enabled Prospect Outreach System (Phase 1) with the following requirements:

        **PROJECT OVERVIEW:**
        - Purpose: AI-enabled prospect outreach system to identify lead intent and provide prequalified leads
        - Communication: SMS-based communication only (Phase 1)
        - Target Region: Australia, Currency: AUD, Language: English only
        - Platform: Web-based admin panel with React JS frontend

        **CORE SYSTEM REQUIREMENTS:**
        1. **User Management & Authentication**
           - Login with Email/Mobile, Remember Me, Forgot Password
           - User Role: Master Admin/System Owner

        2. **Franchise Management**
           - CRUD operations for franchises
           - Fields: Name, Category, Region, Budget, Sub Category
           - Upload franchise brochures, Zoho CRM integration, CSV import

        3. **Document Management System**
           - PDF handling with vector storage
           - RAG implementation for document analysis
           - Map franchises with documents

        4. **Sales Script Management**
           - CRUD operations for scripts
           - Map scripts with specific franchises

        5. **Lead Management System**
           - Import via CSV, Zoho CRM sync, manual entry
           - Display leads with communication history and search

        6. **Question Bank & Prequalification**
           - CRUD operations for questions
           - Mark questions as completed, virtual agent exception handling

        7. **General Settings & Configuration**
           - System messages, holiday messages, agent exceptions

        8. **Meeting Scheduling Integration**
           - Calendly integration with OAuth
           - Automatic booking based on availability

        9. **Analytics & Reporting**
           - SMS count per day, escalation tracking, communication history

        **TECHNICAL INTEGRATIONS:**
        - **Zoho CRM**: Fetch leads, push qualification data and SMS history
        - **SMS Service**: Twilio/Telstra integration with automated sending
        - **AI/RAG**: OpenAI GPT with RAG for document analysis and dynamic responses
        - **Calendar**: Calendly integration for meeting scheduling

        **TECHNICAL STACK:**
        - Frontend: React JS with AdminLTE theme
        - Backend: FastAPI (Python preferred)
        - Database: Vector database (Pinecone/Weaviate) + PostgreSQL
        - Infrastructure: AWS deployment

        **CONSTRAINTS:**
        - Phase 1: SMS only, no voice communication
        - Portrait mode only, no tablet support
        - Manual and unit testing required
        - 5-7 weeks timeline, 306 hours effort

        Please start by designing the overall system architecture, then proceed with implementation focusing on scalable, modular structure with proper error handling and API documentation.
        """

        # Add user suggestions to the task
        user_suggestions = interactive_dev.get_suggestions_for_ai()
        task = base_task + user_suggestions

        # Run the team with interactive development
        print("🚀 Starting AI Development Team...")
        print("💡 Provide suggestions in the terminal above while development is running")
        print("=" * 60)

        # Start development loop that monitors for suggestions
        development_rounds = 0
        max_rounds = 10  # Prevent infinite loops

        while interactive_dev.development_active and development_rounds < max_rounds:
            development_rounds += 1

            # Get latest suggestions
            current_suggestions = interactive_dev.get_suggestions_for_ai()
            current_task = base_task + current_suggestions

            # Display current phase and progress
            task_tracker.display_progress()

            if current_suggestions:
                print(f"\n🔄 Development Round {development_rounds} - Incorporating new suggestions...")
                print("📝 New suggestions being implemented:")
                print(current_suggestions)
            else:
                print(f"\n🔄 Development Round {development_rounds} - Continuing development...")

            print(f"🎯 Current Phase: {task_tracker.get_current_phase()}")
            print("=" * 80)

            # Run team with current task using enhanced console
            try:
                stream = team.run_stream(task=current_task)
                await autogen_console(stream)

                # Mark suggestions as implemented
                for suggestion in interactive_dev.user_suggestions:
                    if not suggestion['implemented']:
                        suggestion['implemented'] = True
                interactive_dev.save_suggestions()

                # Advance to next phase
                task_tracker.advance_phase()

                print(f"\n✅ Round {development_rounds} completed!")
                print(f"🎯 Moving to: {task_tracker.get_current_phase()}")

                # Short break between rounds
                await asyncio.sleep(3)

                # Check if user wants to continue
                if not interactive_dev.development_active:
                    break

            except Exception as e:
                print(f"❌ Error in development round {development_rounds}: {e}")
                break

        print("=" * 60)
        print("✅ Interactive development completed!")
        print(f"📊 Completed {development_rounds} development rounds")

        # Display conversation summary
        autogen_console.display_conversation_summary()

        print("\n📝 Check the generated code and provide feedback for further improvements")
        print("🎯 Development session ended successfully!")
        
    except Exception as e:
        print(f"❌ Error running team: {e}")
        print(f"Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
