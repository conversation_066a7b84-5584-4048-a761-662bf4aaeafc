import uuid
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from app.core.utils.logger.logger import logger

class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for adding request ID and logging request/response"""
    
    async def dispatch(self, request: Request, call_next):
        # Generate request ID
        request_id = str(uuid.uuid4())
        
        # Add request ID to logger context
        logger.info(
            f"Request started: {request.method} {request.url.path}",
            extra={"request_id": request_id}
        )
        
        try:
            # Process request
            response = await call_next(request)
            
            # Log response
            logger.info(
                f"Request completed: {request.method} {request.url.path} - Status: {response.status_code}",
                extra={"request_id": request_id}
            )
            
            return response
            
        except Exception as e:
            # Log error
            logger.error(
                f"Request failed: {request.method} {request.url.path}",
                exc_info=True,
                extra={"request_id": request_id}
            )
            raise 