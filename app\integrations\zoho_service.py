"""Zoho CRM integration service"""
import httpx
from typing import Dict, List, Any
from app.core.settings import settings

class ZohoCRMService:
    def __init__(self):
        self.base_url = "https://www.zohoapis.com.au/crm/v2"
        self.access_token = None

    async def get_access_token(self) -> str:
        """Get access token using refresh token"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://accounts.zoho.com.au/oauth/v2/token",
                    data={
                        "refresh_token": settings.zoho_refresh_token,
                        "client_id": settings.zoho_client_id,
                        "client_secret": settings.zoho_client_secret,
                        "grant_type": "refresh_token"
                    }
                )
                data = response.json()
                self.access_token = data.get("access_token")
                return self.access_token
        except Exception as e:
            print(f"Zoho token error: {e}")
            return None

    async def fetch_leads(self) -> List[Dict[str, Any]]:
        """Fetch leads from Zoho CRM"""
        if not self.access_token:
            await self.get_access_token()

        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/Leads",
                    headers={"Authorization": f"Zoho-oauthtoken {self.access_token}"}
                )
                data = response.json()
                return data.get("data", [])
        except Exception as e:
            print(f"Zoho fetch leads error: {e}")
            return []

    async def update_lead(self, zoho_lead_id: str, update_data: Dict[str, Any]) -> bool:
        """Update lead in Zoho CRM"""
        if not self.access_token:
            await self.get_access_token()

        try:
            async with httpx.AsyncClient() as client:
                response = await client.put(
                    f"{self.base_url}/Leads/{zoho_lead_id}",
                    headers={"Authorization": f"Zoho-oauthtoken {self.access_token}"},
                    json={"data": [update_data]}
                )
                return response.status_code == 200
        except Exception as e:
            print(f"Zoho update lead error: {e}")
            return False
