from passlib.context import Crypt<PERSON>ontext

from app import app_logger
from app.core.utils.exception_manager.exception_handler import exception_handler_obj


class PasswordHandler:
    """Class for hashing and verifying passwords using bcrypt."""
    
    _instance = None

    def __new__(cls):
        """Ensure only one instance exists (singleton pattern)."""
        if cls._instance is None:
            cls._instance = super(<PERSON>word<PERSON><PERSON><PERSON>, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """Initialize the password context with bcrypt."""
        if not hasattr(self, 'pwd_context'):
            self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

    async def hash_password(self, password: str) -> str:
        """
        Hash a password using bcrypt.
        
        Args:
            password (str): Plain text password to hash
            
        Returns:
            str: Hashed password
        """
        try:
            hashed = self.pwd_context.hash(password)
            await app_logger.info(
                message="Password hashed successfully",
                function=f"{self.__class__.__name__}-hash_password"
            )
            return hashed
        except Exception as e:
            await app_logger.exception(
                raw_exception=e,
                message_key="unknown_error",
                function=f"{self.__class__.__name__}-hash_password"
            )
            exception = await exception_handler_obj.manage_exception("unknown_error")
            raise exception

    async def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """
        Verify a password against its hash.
        
        Args:
            plain_password (str): Plain text password to verify
            hashed_password (str): Hashed password to verify against
            
        Returns:
            bool: True if password matches, False otherwise
        """
        try:
            is_valid = self.pwd_context.verify(plain_password, hashed_password)
            await app_logger.info(
                message=f"Password verification completed: {is_valid}",
                function=f"{self.__class__.__name__}-verify_password"
            )
            return is_valid
        except Exception as e:
            await app_logger.exception(
                raw_exception=e,
                message_key="unknown_error",
                function=f"{self.__class__.__name__}-verify_password"
            )
            return False

    def validate_password_strength(self, password: str) -> bool:
        """
        Validate password strength.
        
        Requirements:
        - At least 8 characters long
        - Contains at least one uppercase letter
        - Contains at least one lowercase letter
        - Contains at least one digit
        - Contains at least one special character
        
        Args:
            password (str): Password to validate
            
        Returns:
            bool: True if password meets requirements, False otherwise
        """
        if len(password) < 8:
            return False
        
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;':\",./<>?" for c in password)
        
        return all([has_upper, has_lower, has_digit, has_special])


# Global singleton instance
password_handler = PasswordHandler() 