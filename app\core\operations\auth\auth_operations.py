import secrets
import uuid
from datetime import datetime
from typing import Optional, Dict, Any

from sqlalchemy import or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from fastapi.responses import JSONResponse

from app import app_logger
from app.core.config import settings
from app.core.models.sql_alchemy_models.user_models import User
from app.core.models.pydantic_models.auth_models import (
    UserRegistrationRequest, 
    UserLoginRequest, 
    ForgotPasswordRequest, 
    ResetPasswordRequest,
    UserResponse,
    LoginResponse
)
from app.core.utils.authentication_manager.jwt_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from app.core.utils.authentication_manager.password_handler import password_handler
from app.core.utils.exception_manager.exception_handler import exception_handler_obj
from app.core.utils.exception_manager.custom_exceptions import (
    AuthenticationError,
    ValidationError,
    DatabaseError,
    ResourceNotFoundError
)
from app.core.utils.system_constants.system_messages import client_messages, default_response


class AuthOperations:
    """Authentication operations class for handling user registration, login, and password management"""
    
    def __init__(self):
        self.jwt_handler = JWTHandler()
        # In-memory storage for password reset tokens (in production, use Redis or database)
        self.reset_tokens: Dict[str, Dict[str, Any]] = {}

    async def register_user(self, registration_data: UserRegistrationRequest, db: AsyncSession, language: str = "en") -> JSONResponse:
        """
        Register a new user
        
        Args:
            registration_data: User registration data
            db: Database session
            language: Response language
            
        Returns:
            JSONResponse: Registration response
        """
        response = default_response.copy()
        
        try:
            # Check if email already exists
            email_check = await db.execute(
                select(User).where(User.email == registration_data.email)
            )
            if email_check.scalar_one_or_none():
                return await exception_handler_obj.manage_exception(
                    "email_already_exists",
                    language=language,
                    details={"email": registration_data.email}
                )
            
            # Check if mobile number already exists
            if registration_data.mobile_number:
                mobile_check = await db.execute(
                    select(User).where(User.mobile_number == registration_data.mobile_number)
                )
                if mobile_check.scalar_one_or_none():
                    return await exception_handler_obj.manage_exception(
                        "mobile_already_exists",
                        language=language,
                        details={"mobile_number": registration_data.mobile_number}
                    )
            
            # Validate password strength
            if not password_handler.validate_password_strength(registration_data.password):
                return await exception_handler_obj.manage_exception(
                    "password_policy_violation",
                    language=language
                )
            
            # Hash the password
            hashed_password = await password_handler.hash_password(registration_data.password)
            
            # Create new user
            new_user = User(
                id=uuid.uuid4(),
                first_name=registration_data.first_name,
                last_name=registration_data.last_name,
                email=registration_data.email,
                mobile_number=registration_data.mobile_number,
                password_hash=hashed_password,
                role='admin',
                is_active=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            db.add(new_user)
            await db.commit()
            await db.refresh(new_user)
            
            # Create user response
            user_response = UserResponse(
                id=str(new_user.id),
                first_name=new_user.first_name,
                last_name=new_user.last_name,
                email=new_user.email,
                mobile_number=new_user.mobile_number,
                role=new_user.role,
                is_active=new_user.is_active,
                email_verified_at=new_user.email_verified_at.isoformat() if new_user.email_verified_at else None,
                last_login_at=new_user.last_login_at.isoformat() if new_user.last_login_at else None,
                remember_me_token=new_user.remember_me_token,
                created_at=new_user.created_at.isoformat(),
                updated_at=new_user.updated_at.isoformat()
            )
            
            response.update({
                "success": True,
                "message": client_messages.get(language, {}).get("user_registered_successfully"),
                "data": {"details": {"user": user_response.dict()}},
                "error_code": 0
            })
            
            await app_logger.info(
                message=f"User registered successfully: {new_user.email}",
                function="AuthOperations-register_user"
            )
            
            return JSONResponse(content=response, status_code=201)
            
        except Exception as e:
            await db.rollback()
            await app_logger.exception(
                raw_exception=e,
                message_key="user_creation_failed",
                function="AuthOperations-register_user"
            )
            return await exception_handler_obj.manage_exception(
                "user_creation_failed",
                language=language,
                details={"error": str(e)}
            )

    async def login_user(self, login_data: UserLoginRequest, db: AsyncSession, language: str = "en") -> JSONResponse:
        """
        Authenticate user login
        
        Args:
            login_data: User login data
            db: Database session
            language: Response language
            
        Returns:
            JSONResponse: Login response with JWT token
        """
        response = default_response.copy()
        
        try:
            # Find user by email or mobile number
            user_query = await db.execute(
                select(User).where(
                    or_(
                        User.email == login_data.identifier,
                        User.mobile_number == login_data.identifier
                    )
                )
            )
            user = user_query.scalar_one_or_none()
            
            if not user:
                return await exception_handler_obj.manage_exception(
                    "invalid_user_credentials",
                    language=language,
                    details={"identifier": login_data.identifier}
                )
            
            # Check if account is active
            if not user.is_active:
                return await exception_handler_obj.manage_exception(
                    "account_disabled",
                    language=language,
                    details={"user_id": str(user.id)}
                )
            
            # Verify password
            is_valid = await password_handler.verify_password(login_data.password, user.password_hash)
            if not is_valid:
                return await exception_handler_obj.manage_exception(
                    "invalid_user_credentials",
                    language=language,
                    details={"identifier": login_data.identifier}
                )
            
            # Handle remember_me token
            if login_data.remember_me:
                remember_me_token = secrets.token_urlsafe(32)
                user.remember_me_token = remember_me_token
            else:
                user.remember_me_token = None
            await db.commit()
            await db.refresh(user)

            # Generate JWT token
            token_data = {"user_id": str(user.id), "email": user.email}
            access_token = await self.jwt_handler.create_access_token(
                data=token_data, 
                remember_me=login_data.remember_me
            )
            
            # Create user response
            user_response = UserResponse(
                id=str(user.id),
                first_name=user.first_name,
                last_name=user.last_name,
                email=user.email,
                mobile_number=user.mobile_number,
                role=user.role,
                is_active=user.is_active,
                email_verified_at=user.email_verified_at.isoformat() if user.email_verified_at else None,
                last_login_at=user.last_login_at.isoformat() if user.last_login_at else None,
                remember_me_token=user.remember_me_token,
                created_at=user.created_at.isoformat(),
                updated_at=user.updated_at.isoformat()
            )
            
            # Create login response
            login_response = LoginResponse(
                access_token=access_token,
                token_type="bearer",
                user=user_response
            )
            
            response.update({
                "success": True,
                "message": client_messages.get(language, {}).get("user_authenticated_successfully"),
                "data": {"details": login_response.dict()},
                "error_code": 0
            })
            
            await app_logger.info(
                message=f"User logged in successfully: {user.email}",
                function="AuthOperations-login_user"
            )
            
            return JSONResponse(content=response, status_code=200)
            
        except Exception as e:
            await app_logger.exception(
                raw_exception=e,
                message_key="login_failed",
                function="AuthOperations-login_user"
            )
            return await exception_handler_obj.manage_exception(
                "login_failed",
                language=language,
                details={"error": str(e)}
            )

    async def forgot_password(self, forgot_data: ForgotPasswordRequest, db: AsyncSession, language: str = "en") -> JSONResponse:
        """
        Initiate password reset process
        
        Args:
            forgot_data: Forgot password data
            db: Database session
            language: Response language
            
        Returns:
            JSONResponse: Password reset response
        """
        response = default_response.copy()
        
        try:
            # Find user by email or mobile number
            user_query = await db.execute(
                select(User).where(
                    or_(
                        User.email == forgot_data.identifier,
                        User.mobile_number == forgot_data.identifier
                    )
                )
            )
            user = user_query.scalar_one_or_none()
            
            if not user:
                return await exception_handler_obj.manage_exception("user_not_found", language=language)
            
            # Generate reset token
            reset_token = secrets.token_urlsafe(32)
            
            # Store reset token in memory (in production, use Redis or database)
            self.reset_tokens[reset_token] = {
                "user_id": str(user.id),
                "created_at": datetime.utcnow(),
                "expires_at": datetime.utcnow().timestamp() + 3600  # 1 hour expiry
            }
            
            # In a real application, you would send an email here
            # For this demo, we'll just log the token
            await app_logger.info(
                message=f"Password reset token generated for {user.email}: {reset_token}",
                function="AuthOperations-forgot_password"
            )
            
            response.update({
                "success": True,
                "message": client_messages.get(language, {}).get("password_reset_email_sent"),
                "data": {"details": {"reset_token": reset_token, "expires_in": "1 hour"}},  # Remove this in production
                "error_code": 0
            })
            
            return JSONResponse(content=response, status_code=200)
            
        except Exception as e:
            await app_logger.exception(
                raw_exception=e,
                message_key="unknown_error",
                function="AuthOperations-forgot_password"
            )
            return await exception_handler_obj.manage_exception("unknown_error", language=language)

    async def reset_password(self, reset_data: ResetPasswordRequest, db: AsyncSession, language: str = "en") -> JSONResponse:
        """
        Reset user password using token
        
        Args:
            reset_data: Reset password data
            db: Database session
            language: Response language
            
        Returns:
            JSONResponse: Password reset response
        """
        response = default_response.copy()
        
        try:
            # Validate reset token
            token_info = self.reset_tokens.get(reset_data.token)
            if not token_info:
                return await exception_handler_obj.manage_exception("invalid_reset_token", language=language)
            
            # Check if token has expired
            if datetime.utcnow().timestamp() > token_info["expires_at"]:
                # Clean up expired token
                del self.reset_tokens[reset_data.token]
                return await exception_handler_obj.manage_exception("invalid_reset_token", language=language)
            
            # Validate password strength
            if not password_handler.validate_password_strength(reset_data.new_password):
                return await exception_handler_obj.manage_exception("password_policy_violation", language=language)
            
            # Find user
            user_query = await db.execute(
                select(User).where(User.id == uuid.UUID(token_info["user_id"]))
            )
            user = user_query.scalar_one_or_none()
            
            if not user:
                return await exception_handler_obj.manage_exception("user_not_found", language=language)
            
            # Hash new password
            hashed_password = await password_handler.hash_password(reset_data.new_password)
            
            # Update user password
            user.password_hash = hashed_password
            user.updated_at = datetime.utcnow()
            
            await db.commit()
            
            # Clean up used token
            del self.reset_tokens[reset_data.token]
            
            response.update({
                "success": True,
                "message": client_messages.get(language, {}).get("password_reset_successfully"),
                "data": {"details": {}},
                "error_code": 0
            })
            
            await app_logger.info(
                message=f"Password reset successfully for user: {user.email}",
                function="AuthOperations-reset_password"
            )
            
            return JSONResponse(content=response, status_code=200)
            
        except Exception as e:
            await db.rollback()
            await app_logger.exception(
                raw_exception=e,
                message_key="unknown_error",
                function="AuthOperations-reset_password"
            )
            return await exception_handler_obj.manage_exception("unknown_error", language=language)

    async def logout_user(self, user_id: str, language: str = "en") -> JSONResponse:
        """
        Logout user (simulate token invalidation)
        
        Args:
            user_id: User ID from JWT token
            language: Response language
            
        Returns:
            JSONResponse: Logout response
        """
        response = default_response.copy()
        
        try:
            # In a real application, you would:
            # 1. Add the token to a blacklist
            # 2. Store it in Redis with expiry
            # 3. Remove from active sessions
            
            await app_logger.info(
                message=f"User logged out: {user_id}",
                function="AuthOperations-logout_user"
            )
            
            response.update({
                "success": True,
                "message": client_messages.get(language, {}).get("user_logged_out_successfully"),
                "data": {"details": {}},
                "error_code": 0
            })
            
            return JSONResponse(content=response, status_code=200)
            
        except Exception as e:
            await app_logger.exception(
                raw_exception=e,
                message_key="unknown_error",
                function="AuthOperations-logout_user"
            )
            return await exception_handler_obj.manage_exception("unknown_error", language=language)

    async def validate_token(self, current_user: User, language: str = "en") -> JSONResponse:
        """
        Validate JWT token and return user info
        
        Args:
            current_user: Current authenticated user
            language: Response language
            
        Returns:
            JSONResponse: Token validation response
        """
        response = default_response.copy()
        
        try:
            user_response = UserResponse(
                id=str(current_user.id),
                first_name=current_user.first_name,
                last_name=current_user.last_name,
                email=current_user.email,
                mobile_number=current_user.mobile_number,
                role=current_user.role,
                is_active=current_user.is_active,
                email_verified_at=current_user.email_verified_at.isoformat() if current_user.email_verified_at else None,
                last_login_at=current_user.last_login_at.isoformat() if current_user.last_login_at else None,
                remember_me_token=current_user.remember_me_token,
                created_at=current_user.created_at.isoformat(),
                updated_at=current_user.updated_at.isoformat()
            )
            
            response.update({
                "success": True,
                "message": {
                    "title": "Token Valid",
                    "description": "JWT token is valid and active"
                },
                "data": {
                    "details": {
                        "valid": True,
                        "user": user_response.dict()
                    }
                },
                "error_code": 0
            })
            
            await app_logger.info(
                message=f"Token validated for user: {current_user.email}",
                function="AuthOperations-validate_token"
            )
            
            return JSONResponse(content=response, status_code=200)
            
        except Exception as e:
            await app_logger.exception(
                raw_exception=e,
                message_key="unknown_error",
                function="AuthOperations-validate_token"
            )
            return await exception_handler_obj.manage_exception("unknown_error", language=language) 