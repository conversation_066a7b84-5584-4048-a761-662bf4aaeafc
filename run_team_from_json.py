import asyncio
import os
import json
import sys
import time
from pathlib import Path
from autogen_core import ComponentLoader
from autogen_agentchat.ui import Console

def load_user_suggestions():
    """Load user suggestions from file"""
    suggestions_file = "user_suggestions.json"
    if os.path.exists(suggestions_file):
        try:
            with open(suggestions_file, 'r') as f:
                suggestions = json.load(f)
                # Get only pending suggestions
                pending = [s for s in suggestions if not s.get('implemented', False)]
                return pending
        except:
            return []
    return []

def format_suggestions_for_ai(suggestions):
    """Format suggestions for AI team"""
    if not suggestions:
        return ""

    formatted = "\n\n🔥 USER SUGGESTIONS TO IMPLEMENT:\n"
    for suggestion in suggestions:
        priority = "🔥 HIGH PRIORITY" if suggestion.get('priority') == 'high' else "📝 NORMAL"
        formatted += f"- {priority}: {suggestion['suggestion']}\n"

    formatted += "\nPlease incorporate these suggestions into your development process.\n"
    return formatted

async def main():
    """Load and run the AI development team from JSON configuration with suggestion monitoring"""

    # Set up environment variables (replace with your actual values)
    # Set OpenAI API key
    os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"

    print("✅ OpenAI API key configured successfully")
    print("🎯 Starting AI Development Team with Real-time Suggestion Monitoring")
    print("💡 Open another terminal and run: python suggestion_listener.py")
    print("📝 You can provide suggestions during development in that terminal")

    try:
        # Load the team configuration from JSON file
        config_file = sys.argv[1] if len(sys.argv) > 1 else "ai_dev_team_config.json"
        with open(config_file, "r", encoding="utf-8") as f:
            config_data = json.load(f)

        # Load the team using ComponentLoader
        loader = ComponentLoader()
        team = loader.load_component(config_data)

        # Define the task
        task = """
        Create a comprehensive system architecture diagram for the GrowthHive AI-enabled prospect outreach system.

        **SYSTEM OVERVIEW:**
        The GrowthHive backend is an AI-enabled prospect outreach system with the following key components:
        - FastAPI backend with comprehensive APIs
        - Supabase/PostgreSQL with vector support (pgvector)
        - RAG (Retrieval-Augmented Generation) system for document analysis
        - SMS integration (Twilio/Kudocity) for prospect communication
        - Zoho CRM integration for lead management
        - Vector databases for intelligent document search
        - Authentication and authorization system
        - Real-time notifications and updates

        **TASK REQUIREMENTS:**
        1. **Create a detailed system architecture diagram** showing:
           - All major system components and their relationships
           - Data flow between components
           - External service integrations (Zoho CRM, SMS providers, OpenAI)
           - Database architecture (PostgreSQL + vector storage)
           - API layer structure
           - Authentication flow

        2. **Include these specific components:**
           - FastAPI Application Layer
           - Authentication & Authorization Module
           - Franchise Management System
           - Lead Management System
           - Document Management with RAG
           - Vector Database (pgvector)
           - SMS Integration Service
           - Zoho CRM Integration
           - AI/RAG Processing Pipeline
           - Real-time Notification System

        3. **Show integration points:**
           - External APIs (Zoho, SMS providers, OpenAI)
           - Database connections
           - File storage systems
           - Authentication flows

        **OUTPUT FORMAT:**
        Please create the architecture diagram in Mermaid format that can be easily visualized and understood by the development team.

        Start with the AI Architect to design and create this comprehensive system architecture diagram.
        """

        # Run the team and stream the conversation to console
        print("🚀 Starting AI Development Team...")
        print("=" * 60)

        stream = team.run_stream(task=task)
        await Console(stream)

        print("=" * 60)
        print("✅ Team execution completed!")

    except Exception as e:
        print(f"❌ Error running team: {e}")
        print(f"Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        print("\nTroubleshooting tips:")
        print("1. Make sure your OPENAI_API_KEY is set correctly")
        print("2. Verify ai_dev_team_config.json exists and is valid")
        print("3. Check that all required dependencies are installed")

if __name__ == "__main__":
    asyncio.run(main())
