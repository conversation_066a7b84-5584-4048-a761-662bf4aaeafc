#!/usr/bin/env python3
"""
Setup script for AutoGen development environment
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python 3.8+ required, found {version.major}.{version.minor}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_requirements():
    """Install required packages"""
    requirements_file = "autogen_requirements.txt"
    if not os.path.exists(requirements_file):
        print(f"❌ Requirements file {requirements_file} not found")
        return False
    
    return run_command(
        f"{sys.executable} -m pip install -r {requirements_file}",
        "Installing AutoGen requirements"
    )

def setup_environment():
    """Setup environment variables"""
    env_file = ".env"
    if not os.path.exists(env_file):
        print("📝 Creating .env file template...")
        env_template = """# AutoGen Environment Variables
OPENAI_API_KEY=your_openai_api_key_here

# Optional: Other AI Model APIs
ANTHROPIC_API_KEY=your_anthropic_key_here

# AutoGen Studio Configuration
AUTOGEN_STUDIO_PORT=8081
AUTOGEN_STUDIO_HOST=localhost

# Development Settings
DEBUG=true
LOG_LEVEL=INFO
"""
        with open(env_file, 'w') as f:
            f.write(env_template)
        print("✅ .env template created")
        print("⚠️  Please edit .env file and add your API keys")
    else:
        print("✅ .env file already exists")

def verify_installation():
    """Verify AutoGen installation"""
    try:
        import autogen_agentchat
        import autogen_core
        import autogen_ext
        print("✅ AutoGen packages imported successfully")
        return True
    except ImportError as e:
        print(f"❌ AutoGen import failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 AutoGen Development Environment Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Install requirements
    if not install_requirements():
        return False
    
    # Verify installation
    if not verify_installation():
        return False
    
    # Setup environment
    setup_environment()
    
    print("\n" + "=" * 50)
    print("✅ AutoGen setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Edit .env file and add your OpenAI API key")
    print("2. Run: python run_autogen_with_studio.py")
    print("3. Access web interface at: http://localhost:8081")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
