"""Main FastAPI application"""
from fastapi import FastAP<PERSON>, Depends
from fastapi.middleware.cors import CORSMiddleware
from app.api.v1 import auth
from app.core.database import engine, Base
from app.core.settings import settings

# Create FastAPI app
app = FastAPI(
    title="GrowthHive Prospect Outreach API",
    description="AI-enabled prospect outreach system for franchise lead qualification",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth.router, prefix="/api/v1")

@app.on_event("startup")
async def startup_event():
    """Create database tables on startup"""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

@app.get("/")
async def root():
    return {"message": "GrowthHive Prospect Outreach API", "status": "running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "version": "1.0.0"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
