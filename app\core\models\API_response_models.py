from app.core.models.pydantic_models.response_model import ResponseModel
from app.core.utils.system_constants.system_error_code import pre_define_error
from app.core.utils.system_constants.system_messages import client_messages

# Get English messages
en_messages = client_messages['en']
en_errors = pre_define_error['en']

# Authentication Response Models
user_registration_response_model = {
    201: {
        "model": ResponseModel,
        "description": "User registered successfully",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": en_messages["user_registered_successfully"],
                    "data": {
                        "details": {
                            "user_id": "123e4567-e89b-12d3-a456-426614174000",
                            "email": "<EMAIL>",
                            "first_name": "<PERSON>",
                            "last_name": "<PERSON>e"
                        }
                    },
                    "error_code": 0
                }
            }
        }
    },
    409: {
        "model": ResponseModel,
        "description": "Email or mobile already exists",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": en_errors["email_already_exists"]["message"],
                    "data": {"details": None},
                    "error_code": en_errors["email_already_exists"]["code"]
                }
            }
        }
    },
    400: {
        "model": ResponseModel,
        "description": "Password policy violation",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": en_errors["password_policy_violation"]["message"],
                    "data": {"details": None},
                    "error_code": en_errors["password_policy_violation"]["code"]
                }
            }
        }
    },
    500: {
        "model": ResponseModel,
        "description": "Internal Server Error",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": en_errors["user_creation_failed"]["message"],
                    "data": {"details": None},
                    "error_code": en_errors["user_creation_failed"]["code"]
                }
            }
        }
    }
}

user_login_response_model = {
    200: {
        "model": ResponseModel,
        "description": "User authenticated successfully",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": en_messages["user_authenticated_successfully"],
                    "data": {
                        "details": {
                            "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                            "token_type": "bearer",
                            "user": {
                                "id": "123e4567-e89b-12d3-a456-426614174000",
                                "first_name": "John",
                                "last_name": "Doe",
                                "email": "<EMAIL>",
                                "mobile_number": "+1234567890"
                            }
                        }
                    },
                    "error_code": 0
                }
            }
        }
    },
    401: {
        "model": ResponseModel,
        "description": "Invalid credentials",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": en_errors["invalid_user_credentials"]["message"],
                    "data": {"details": None},
                    "error_code": en_errors["invalid_user_credentials"]["code"]
                }
            }
        }
    },
    404: {
        "model": ResponseModel,
        "description": "User not found",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": en_errors["user_not_found"]["message"],
                    "data": {"details": None},
                    "error_code": en_errors["user_not_found"]["code"]
                }
            }
        }
    }
}

user_logout_response_model = {
    200: {
        "model": ResponseModel,
        "description": "User logged out successfully",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": en_messages["user_logged_out_successfully"],
                    "data": {"details": {}},
                    "error_code": 0
                }
            }
        }
    },
    401: {
        "model": ResponseModel,
        "description": "Invalid or expired token",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": en_errors["invalid_access_token"]["message"],
                    "data": {"details": None},
                    "error_code": en_errors["invalid_access_token"]["code"]
                }
            }
        }
    }
}

forgot_password_response_model = {
    200: {
        "model": ResponseModel,
        "description": "Password reset email sent",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": en_messages["password_reset_email_sent"],
                    "data": {
                        "details": {
                            "reset_token": "reset-token-123",
                            "expires_in": "1 hour"
                        }
                    },
                    "error_code": 0
                }
            }
        }
    },
    404: {
        "model": ResponseModel,
        "description": "User not found",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": en_errors["user_not_found"]["message"],
                    "data": {"details": None},
                    "error_code": en_errors["user_not_found"]["code"]
                }
            }
        }
    }
}

reset_password_response_model = {
    200: {
        "model": ResponseModel,
        "description": "Password reset successfully",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": en_messages["password_reset_successfully"],
                    "data": {"details": {}},
                    "error_code": 0
                }
            }
        }
    },
    400: {
        "model": ResponseModel,
        "description": "Invalid reset token",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": en_errors["invalid_reset_token"]["message"],
                    "data": {"details": None},
                    "error_code": en_errors["invalid_reset_token"]["code"]
                }
            }
        }
    }
}

validate_token_response_model = {
    200: {
        "model": ResponseModel,
        "description": "Token validated successfully",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Token Valid",
                        "description": "JWT token is valid and active"
                    },
                    "data": {
                        "details": {
                            "valid": True,
                            "user": {
                                "id": "123e4567-e89b-12d3-a456-426614174000",
                                "first_name": "John",
                                "last_name": "Doe",
                                "email": "<EMAIL>",
                                "mobile_number": "+1234567890"
                            }
                        }
                    },
                    "error_code": 0
                }
            }
        }
    },
    401: {
        "model": ResponseModel,
        "description": "Invalid or expired token",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Token Invalid",
                        "description": "JWT token is invalid or expired"
                    },
                    "data": {"details": None},
                    "error_code": 4004
                }
            }
        }
    }
}

create_vendor_relations = {
    200: {
        "model": ResponseModel,
        "description": "Success",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Vendor Relation Created",
                        "description": "Vendor relation created successfully"
                    },
                    "data": {
                        "details": {
                            "id": "1234567890",
                            "employee_name": "John Doe",
                            "relationship": "Employee"
                        }
                    },
                    "error_code": 0
                }
            }
        }
    },
    401: {
        "model": ResponseModel,
        "description": "Unauthorized",
        "content": {
            "application/json": {
                "example": {
                    "data": {},
                    "error_code": en_errors["invalid_user_credentials"]["code"],
                    "message": en_errors["invalid_user_credentials"]["message"],
                    "success": False
                }
            }
        }
    },
    403: {
        "model": ResponseModel,
        "description": "Forbidden",
        "content": {
            "application/json": {
                "example": {
                    "data": {},
                    "error_code": en_errors["user_not_verified"]["code"],
                    "message": en_errors["user_not_verified"]["message"],
                    "success": False
                }
            }
        }
    },
}

vendor_relations_list = {
    200: {
        "model": ResponseModel,
        "description": "Success",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Vendor Relations Listed",
                        "description": "Vendor relations retrieved successfully"
                    },
                    "data": {
                        "details": [
                            {
                                "id": "1234567890",
                                "employee_name": "John Doe",
                                "relationship": "Employee"
                            },
                            {
                                "id": "0987654321",
                                "employee_name": "Jane Smith",
                                "relationship": "Manager"
                            }
                        ]
                    },
                    "error_code": 0
                }
            }
        }
    },
}

vendor_relations_delete = {
    200: {
        "model": ResponseModel,
        "description": "Success",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Vendor Relation Deleted",
                        "description": "Vendor relation deleted successfully"
                    },
                    "data": {},
                    "error_code": 0
                }
            }
        }
    },
}

vendor_status_update = {
    200: {
        "model": ResponseModel,
        "description": "Success",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Vendor Relation Status Updated",
                        "description": "Vendor relation status updated successfully"
                    },
                    "data": {},
                    "error_code": 0
                }
            }
        }
    },

    401: {
        "model": ResponseModel,
        "description": "Unauthorized",
        "content": {
            "application/json": {
                "example": {
                    "data": {},
                    "error_code": en_errors["invalid_user_credentials"]["code"],
                    "message": en_errors["invalid_user_credentials"]["message"],
                    "success": False
                }
            }
        }
    },
    403: {
        "model": ResponseModel,
        "description": "Forbidden",
        "content": {
            "application/json": {
                "example": {
                    "data": {},
                    "error_code": en_errors["user_not_verified"]["code"],
                    "message": en_errors["user_not_verified"]["message"],
                    "success": False
                }
            }
        }
    },
    404: {
        "model": ResponseModel,
        "description": "Not Found",
        "content": {
            "application/json": {
                "example": {
                    "data": {},
                    "error_code": en_errors["vendor_relation_not_found"]["code"],
                    "message": en_errors["vendor_relation_not_found"]["message"],
                    "success": False
                }
            }
        }
    },
    500: {
        "model": ResponseModel,
        "description": "Internal Server Error",
        "content": {
            "application/json": {
                "example": {
                    "data": {},
                    "error_code": en_errors["unknown_error"]["code"],
                    "message": en_errors["unknown_error"]["message"],
                    "success": False
                }
            }
        }
    }
}

business_details_response_model = {
    200: {
        "model": ResponseModel,
        "description": "Business Details Saved Successfully",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Business Details Saved",
                        "description": "Business details saved successfully"
                    },
                    "data": {
                        "details": {
                            "business_id": "UID12347",
                            "onboarding_status": "pending"
                        }
                    },
                    "error_code": 0
                }
            }
        }
    },
    404: {
        "model": ResponseModel,
        "description": "User Not Found",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": en_errors["user_not_found"]["message"],
                    "data": {
                        "details": None
                    },
                    "error_code": en_errors["user_not_found"]["code"]
                }
            }
        }
    },
    500: {
        "model": ResponseModel,
        "description": "Internal Server Error",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": en_errors["business_details_save_error"]["message"],
                    "data": {
                        "details": None
                    },
                    "error_code": en_errors["business_details_save_error"]["code"]
                }
            }
        }
    }
}

find_area_response_model = {
    200: {
        "model": ResponseModel,
        "description": "Success",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Location Found",
                        "description": "Location information retrieved successfully"
                    },
                    "data": {
                        "details": {
                            "city": "New York",
                            "state": "NY"
                        }
                    },
                    "error_code": 0
                }
            }
        }
    },
    404: {
        "model": ResponseModel,
        "description": "Not Found",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": en_errors["location_not_found"]["message"],
                    "data": {"details": None},
                    "error_code": en_errors["location_not_found"]["code"]
                }
            }
        }
    }
}

vendor_profile_status_response_model = {
    200: {
        "model": ResponseModel,
        "description": "Success",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Vendor Profile Status Retrieved",
                        "description": "Vendor profile status retrieved successfully"
                    },
                    "data": {
                        "details": {
                            "onboarding_status": "business_details_completed",
                            "business_type": "restaurant",
                            "sub_type": "fine_dining",
                            "account_verification": True
                        }
                    },
                    "error_code": 0
                }
            }
        }
    },
    404: {
        "model": ResponseModel,
        "description": "Not Found",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": en_errors["vendor_not_found"]["message"],
                    "data": {"details": None},
                    "error_code": en_errors["vendor_not_found"]["code"]
                }
            }
        }
    }
}

vendor_list_response_model = {
    200: {
        "model": ResponseModel,
        "description": "Vendor list fetched successfully",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Vendor List Fetched",
                        "description": "Vendor list retrieved successfully"
                    },
                    "data": {
                        "vendors": [
                            {
                                "id": "UID12345",
                                "notes": "Preferred vendor",
                                "onboarding_status": {"step": "In Progress"},
                                "payment_is_ACH": True,
                                "admin_change_status": False
                            }
                        ],
                        "pagination": {
                            "skip": 0,
                            "limit": 10,
                            "count": 1
                        }
                    },
                    "error_code": 0
                }
            }
        }
    },
    500: {
        "model": ResponseModel,
        "description": "Internal Server Error",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": en_errors["vendor_profile_fetch_error"]["message"],
                    "data": {
                        "vendors": None
                    },
                    "error_code": en_errors["vendor_profile_fetch_error"]["code"]
                }
            }
        }
    }
}

# Vendor onboarding response models
vendor_profile_details_response = {
    200: {
        "model": ResponseModel,
        "description": "Success",
        "content": {
            "application/json": {
                "example": {
                    "data": {
                        "details": {
                            "vendor_id": "123e4567-e89b-12d3-a456-426614174000",
                            "business_name": "Sample Business",
                            "business_type": "Corporation",
                            "status": "active",
                            "profile_completion": 75
                        }
                    },
                    "error_code": 0,
                    "message": {
                        "title": "Vendor Profile Details Retrieved",
                        "description": "Vendor profile details retrieved successfully"
                    },
                    "success": True
                }
            }
        }
    },
    404: {
        "model": ResponseModel,
        "description": "Not Found",
        "content": {
            "application/json": {
                "example": {
                    "data": {"details": None},
                    "error_code": en_errors["vendor_profile_not_found"]["code"],
                    "message": en_errors["vendor_profile_not_found"]["message"],
                    "success": False
                }
            }
        }
    }
}

vendor_status_update_response = {
    200: {
        "model": ResponseModel,
        "description": "Success",
        "content": {
            "application/json": {
                "example": {
                    "data": {
                        "details": {
                            "vendor_id": "123e4567-e89b-12d3-a456-426614174000",
                            "status": "active",
                            "updated_at": "2024-03-21T10:00:00Z"
                        }
                    },
                    "error_code": 0,
                    "message": {
                        "title": "Vendor Status Updated",
                        "description": "Vendor status updated successfully"
                    },
                    "success": True
                }
            }
        }
    }
}

vendor_reinvite_response = {
    200: {
        "model": ResponseModel,
        "description": "Success",
        "content": {
            "application/json": {
                "example": {
                    "data": {
                        "details": {
                            "vendor_id": "123e4567-e89b-12d3-a456-426614174000",
                            "email_sent": True,
                            "sent_at": "2024-03-21T10:00:00Z"
                        }
                    },
                    "error_code": 0,
                    "message": {
                        "title": "Vendor Reinvite Sent",
                        "description": "Vendor reinvite sent successfully"
                    },
                    "success": True
                }
            }
        }
    }
}

vendor_profile_details_model = {
    200: {
        "model": ResponseModel,
        "description": "Success",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Vendor Profile Details",
                        "description": "Vendor profile details fetched successfully."
                    },
                    "data": {
                        "details": {
                            "first_name": "John",
                            "last_name": "Doe",
                            "email": "<EMAIL>",
                            "phone": "1234567890",
                            "role_name": "Vendor",
                            "status": "active",
                            "approval_Status": True,
                        }
                    },
                    "error_code": 0
                }
            }
        }
    },
    404: {
        "model": ResponseModel,
        "description": "Not Found",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Vendor Not Found",
                        "description": "No vendor profile found for the given user."
                    },
                    "data": {"details": None},
                    "error_code": 5005
                }
            }
        }
    }
}

vendor_business_details_model = {
    200: {
        "model": ResponseModel,
        "description": "Success",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Vendor Business Details",
                        "description": "Vendor business details fetched successfully."
                    },
                    "data": {
                        "details": {
                            "business_name": "ABC Construction LLC",
                            "ein_tin": "12-3456789",
                            "email": "<EMAIL>",
                            "phone": "************",
                            "logo": "https://example.com/logo.png",
                            "business_addresses": [
                                {
                                    "address_type": "business",
                                    "street1": "123 Main St",
                                    "street2": "Suite 100",
                                    "city": "New York",
                                    "state": "NY",
                                    "zip_code": "10001"
                                }
                            ]
                        }
                    },
                    "error_code": 0
                }
            }
        }
    },
    404: {
        "model": ResponseModel,
        "description": "Not Found",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Business Details Not Found",
                        "description": "No business details found for the specified vendor."
                    },
                    "data": {"details": None},
                    "error_code": 404
                }
            }
        }
    }
}

vendor_relation_details_model = {
    200: {
        "model": ResponseModel,
        "description": "Success",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Vendor Relation Details",
                        "description": "Vendor existing relation details fetched successfully."
                    },
                    "data": {
                        "details": {
                            "employee_relations": [
                                {
                                    "employee_name": "John Smith",
                                    "relationship": "Business Partner"
                                },
                                {
                                    "employee_name": "Jane Doe",
                                    "relationship": "Spouse"
                                }
                            ]
                        }
                    },
                    "error_code": 0
                }
            }
        }
    },
    404: {
        "model": ResponseModel,
        "description": "Not Found",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Relation Details Not Found",
                        "description": "No existing relation details found for the specified vendor."
                    },
                    "data": {"details": None},
                    "error_code": 404
                }
            }
        }
    }
}

business_details_update_response_model = {
    200: {
        "model": ResponseModel,
        "description": "Business Details Updated Successfully",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Business Details Updated",
                        "description": "The business details have been updated successfully."
                    },
                    "data": {
                        "details": {
                            "business_name": "Updated Business Name",
                            "logo_url": "https://example.com/updated_logo.png",
                            "onboarding_status": "business_details_completed"
                        }
                    },
                    "error_code": 0
                }
            }
        }
    },
    404: {
        "model": ResponseModel,
        "description": "Business Details Not Found",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Business Details Not Found",
                        "description": "No business details found for this user."
                    },
                    "data": {
                        "details": None
                    },
                    "error_code": 404
                }
            }
        }
    },
    500: {
        "model": ResponseModel,
        "description": "Internal Server Error",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Something Went Wrong",
                        "description": "An unexpected error occurred while updating business details."
                    },
                    "data": {
                        "details": None
                    },
                    "error_code": 5000
                }
            }
        }
    }
}

vendor_personal_details_response_model = {
    200: {
        "model": ResponseModel,
        "description": "Success",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Vendor Personal Details",
                        "description": "Vendor personal details saved successfully."
                    },
                    "data": {
                        "details": {
                            "first_name": "John",
                            "last_name": "Doe",
                            "email": "<EMAIL>",
                            "phone": "1234567890",
                            "role_name": "Vendor",
                            "approval_status": True
                        }
                    },
                    "error_code": 0
                }
            }
        }
    },
    400: {
        "model": ResponseModel,
        "description": "Bad Request",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Invalid Request",
                        "description": "Invalid personal details provided."
                    },
                    "data": {"details": None},
                    "error_code": 400
                }
            }
        }
    },
    404: {
        "model": ResponseModel,
        "description": "Not Found",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Vendor Not Found",
                        "description": "No vendor profile found for the given user."
                    },
                    "data": {"details": None},
                    "error_code": 5005
                }
            }
        }
    },
    500: {
        "model": ResponseModel,
        "description": "Internal Server Error",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Server Error",
                        "description": "An error occurred while processing your request."
                    },
                    "data": {"details": None},
                    "error_code": 500
                }
            }
        }
    }
}

vendor_complete_relation_request_model = {
    200: {
        "model": ResponseModel,
        "description": "Success",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Vendor Relation Details Completed",
                        "description": "Vendor relation details completed successfully"
                    },
                    "data": {
                        "details": {
                            "user_id": "sgdhjfggjhj",
                            "has_relationship": False
                        }
                    },
                    "error_code": 0
                }
            }
        }
    }
}

vendor_profile_section_status_update_response_model = {
    200: {
        "model": ResponseModel,
        "description": "Profile section status updated successfully.",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Profile Status Updated",
                        "description": "Vendor profile status updated successfully."
                    },
                    "data": {
                        "details": {
                            "profile_section": "personal_details",
                            "status": "Submitted",
                            "notes": "Submitted for review",
                            "reason": "Initial submission"
                        }
                    },
                    "error_code": 0
                }
            }
        }
    },
    404: {
        "model": ResponseModel,
        "description": "Profile section not found.",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Profile Section Not Found",
                        "description": "The specified profile section does not exist."
                    },
                    "data": {"details": None},
                    "error_code": 3101
                }
            }
        }
    },
    500: {
        "model": ResponseModel,
        "description": "Internal Server Error.",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Profile Status Update Failed",
                        "description": "Failed to update the vendor profile status."
                    },
                    "data": {"details": None},
                    "error_code": 3201
                }
            }
        }
    }
}

business_hours_response_model = {
    200: {
        "description": "Successful response",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Success",
                        "description": "Business hours fetched successfully"
                    },
                    "data": {
                        "timezone": "UTC",
                        "same_for_all_days": True,
                        "days": {
                            "Monday": {
                                "is_active": True,
                                "is_24_hours": False,
                                "start_time": "09:00",
                                "end_time": "17:00"
                            }
                        }
                    },
                    "error_code": 0
                }
            }
        }
    },
    400: {
        "description": "Bad Request",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Validation Error",
                        "description": "Invalid business hours data"
                    },
                    "data": None,
                    "error_code": 400
                }
            }
        }
    },
    404: {
        "description": "Not Found",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Not Found",
                        "description": "No business hours set"
                    },
                    "data": None,
                    "error_code": 404
                }
            }
        }
    }
}

vendor_service_type_response_model = {
    200: {
        "model": ResponseModel,
        "description": "Vendor service types fetched/updated successfully",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Vendor Service Types",
                        "description": "Vendor service types fetched/updated successfully."
                    },
                    "data": {
                        "details": {
                            "roles": [
                                "Trade Vendor",
                                "General Contractor (Construction)"
                            ],
                            "service_types": [
                                {"id": 1, "name": "Clean and Secure", "detail": "Cleaning and securing property"},
                                {"id": 2, "name": "Countertops", "detail": "Countertop installation and repair"}
                            ],
                            "remarks": "Sample remarks or comments."
                        }
                    },
                    "error_code": 0
                }
            }
        }
    },
    201: {
        "model": ResponseModel,
        "description": "Vendor service types created successfully",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Vendor Service Types Created",
                        "description": "Vendor service types created successfully."
                    },
                    "data": {},
                    "error_code": 0
                }
            }
        }
    },
    400: {
        "model": ResponseModel,
        "description": "Bad Request",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Invalid Request",
                        "description": "Invalid service type data provided."
                    },
                    "data": {"details": None},
                    "error_code": 400
                }
            }
        }
    },
    404: {
        "model": ResponseModel,
        "description": "Not Found",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Not Found",
                        "description": "No service type data found for the vendor."
                    },
                    "data": {"details": None},
                    "error_code": 404
                }
            }
        }
    },
    500: {
        "model": ResponseModel,
        "description": "Internal Server Error",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Server Error",
                        "description": "An error occurred while processing your request."
                    },
                    "data": {"details": None},
                    "error_code": 500
                }
            }
        }
    }
}

service_type_response_model = {
    200: {
        "model": ResponseModel,
        "description": "Service type fetched successfully",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Service Type Fetched Successfully",
                        "description": "Service type fetched successfully"
                    },
                    "data": {
                        "details": {
                            "service_type": "Cleaning and securing property"
                        }
                    },
                    "error_code": 0
                }
            }
        }
    },
},

service_area_response_model = {
    201: {
        "model": ResponseModel,
        "description": "Service Area Created Successfully",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Service Area Created",
                        "description": "The service area has been created successfully."
                    },
                    "data": {"details": {"service_area_id": "UUID"}},
                    "error_code": 0
                }
            }
        }
    },
    400: {
        "model": ResponseModel,
        "description": "Bad Request",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Invalid Input",
                        "description": "The input data is invalid."
                    },
                    "data": {"details": None},
                    "error_code": 4000
                }
            }
        }
    },
    500: {
        "model": ResponseModel,
        "description": "Internal Server Error",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Service Area Save Error",
                        "description": "An error occurred while saving the service area."
                    },
                    "data": {"details": None},
                    "error_code": 6001
                }
            }
        }
    }
}

service_area_list_response_model = {
    200: {
        "model": ResponseModel,
        "description": "Service Areas Listed Successfully",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Service Areas Listed Successfully",
                        "description": "The service areas have been listed successfully."
                    },
                    "data": {
                        "details": [
                            {
                                "service_area_id": "0291017c-6c32-4834-bae8-be042c36099c",
                                "city": "New York",
                                "state": "NY",
                                "location_points": [
                                    {"lat": 40.7128, "lng": -74.0060},
                                    {"lat": 40.7138, "lng": -74.0050}
                                ]
                            }
                        ],
                        "pagination": {
                            "current_page": 1,
                            "total_pages": 1,
                            "items_per_page": 10,
                            "total_items": 1
                        }
                    },
                    "error_code": 0
                }
            }
        }
    },
    400: {
        "model": ResponseModel,
        "description": "Bad Request",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Invalid Input",
                        "description": "The input data is invalid."
                    },
                    "data": {"details": None},
                    "error_code": 4000
                }
            }
        }
    },
    500: {
        "model": ResponseModel,
        "description": "Internal Server Error",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Service Area List Error",
                        "description": "An error occurred while fetching the service area list."
                    },
                    "data": {"details": None},
                    "error_code": 6003
                }
            }
        }
    }
}

service_area_update_response_model = {
    200: {
        "model": ResponseModel,
        "description": "Service Area Updated Successfully",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Service Area Updated",
                        "description": "The service area has been updated successfully."
                    },
                    "data": {"details": {"service_area_id": "UUID"}},
                    "error_code": 0
                }
            }
        }
    },
    404: {
        "model": ResponseModel,
        "description": "Service Area Not Found",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Service Area Not Found",
                        "description": "The service area was not found."
                    },
                    "data": {"details": None},
                    "error_code": 6004
                }
            }
        }
    },
    500: {
        "model": ResponseModel,
        "description": "Internal Server Error",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Service Area Update Error",
                        "description": "An error occurred while updating the service area."
                    },
                    "data": {"details": None},
                    "error_code": 6004
                }
            }
        }
    }
}

service_area_delete_response_model = {
    200: {
        "model": ResponseModel,
        "description": "Service Area Deleted Successfully",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Service Area Deleted",
                        "description": "The service area has been deleted successfully."
                    },
                    "data": {"details": {"service_area_id": "UUID"}},
                    "error_code": 0
                }
            }
        }
    },
    404: {
        "model": ResponseModel,
        "description": "Service Area Not Found",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Service Area Not Found",
                        "description": "The service area was not found."
                    },
                    "data": {"details": None},
                    "error_code": 6004
                }
            }
        }
    }
}

service_area_details_response_model = {
    200: {
        "model": ResponseModel,
        "description": "Service Area Details Retrieved Successfully",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Service Area Details Retrieved",
                        "description": "The service area details have been retrieved successfully."
                    },
                    "data": {
                        "details": {
                            "service_area_id": "0291017c-6c32-4834-bae8-be042c36099c",
                            "city": "New York",
                            "state": "NY",
                            "location_points": [
                                {"lat": 40.7128, "lng": -74.0060},
                                {"lat": 40.7138, "lng": -74.0050}
                            ],
                            "maintenance_capacity": 100,
                            "turnover_capacity": 200,
                            "initial_rehab_capacity": 50,
                            "total_service_capacity": 350,
                            "vendor_remarks": "This is a test service area",
                            "inherit_global_working_hours": True,
                            "same_for_all_days": True,
                            "emergency_and_after_hours_coverage": True,
                            "emergency_cost": 100,
                            "kairos_approved": True,
                            "notes": "This is a test service area",
                            "action_reason": "This is a test service area",
                            "working_hours": [
                                {
                                    "day_of_week": "Monday",
                                    "start_time": "09:00",
                                    "end_time": "17:00",
                                    "is_24_hours": False
                                },
                                {
                                    "day_of_week": "Tuesday",
                                    "start_time": "09:00",
                                    "end_time": "17:00",
                                    "is_24_hours": False
                                }
                            ],
                            "emergency_hours": [
                                {
                                    "day_of_week": "Wednesday",
                                    "start_time": "09:00",
                                    "end_time": "17:00",
                                    "is_24_hours": False
                                }
                            ],
                            "service_types": [
                                {"id": 1, "name": "Service Type 1"},
                                {"id": 2, "name": "Service Type 2"}
                            ]
                        }
                    },
                    "error_code": 0
                }
            }
        }
    },
    404: {
        "model": ResponseModel,
        "description": "Service Area Not Found",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Service Area Not Found",
                        "description": "The service area was not found."
                    },
                    "data": {"details": None},
                    "error_code": 3105
                }
            }
        }
    }
}

get_service_area_by_id_response_model = {
    200: {
        "model": ResponseModel,
        "description": "Service Area Lat Long Retrieved Successfully",
        "content": {
            "application/json": {
                "example": {
                    "success": True,
                    "message": {
                        "title": "Service Area Retrieved",
                        "description": "The service area has been retrieved successfully."
                    },
                    "data": {
                        "details": {
                            "title": "Service Area Lat Long",
                            "location_points": [
                                {"lat": 40.7128, "lng": -74.0060},
                                {"lat": 40.7138, "lng": -74.0050}
                            ],
                            "city": "New York",
                            "state": "NY"
                        }
                    },
                    "error_code": 0
                }
            }
        }
    },
    404: {
        "model": ResponseModel,
        "description": "Service Area Not Found",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Service Area Not Found",
                        "description": "The service area was not found."
                    },
                    "data": {"details": None},
                    "error_code": 3105
                }
            }
        }
    }
}
