version: '3.8'

services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: growthhive
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  app:
    build: .
    ports:
      - "8000:8000"
    depends_on:
      - db
    environment:
      DATABASE_URL: ****************************************
    volumes:
      - .:/app

volumes:
  postgres_data:
