{"timestamp": "2025-06-06T12:47:48.036516", "level": "ERROR", "message": "Failed to decode token", "module": "jwt_handler", "function": "decode_token", "line": 116, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jws.py\", line 176, in _load\n    signing_input, crypto_segment = jwt.rsplit(b\".\", 1)\nValueError: not enough values to unpack (expected 2, got 1)\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jwt.py\", line 142, in decode\n    payload = jws.verify(token, key, algorithms, verify=verify_signature)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jws.py\", line 70, in verify\n    header, payload, signing_input, signature = _load(token)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jws.py\", line 180, in _load\n    raise JWSError(\"Not enough segments\")\njose.exceptions.JWSError: Not enough segments\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/utils/authentication_manager/jwt_handler.py\", line 102, in decode_token\n    payload = jwt.decode(\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jwt.py\", line 144, in decode\n    raise JWTError(e)\njose.exceptions.JWTError: Not enough segments"}
{"timestamp": "2025-06-06T12:52:36.794441", "level": "ERROR", "message": "Failed to decode token", "module": "jwt_handler", "function": "decode_token", "line": 116, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jws.py\", line 176, in _load\n    signing_input, crypto_segment = jwt.rsplit(b\".\", 1)\nValueError: not enough values to unpack (expected 2, got 1)\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jwt.py\", line 142, in decode\n    payload = jws.verify(token, key, algorithms, verify=verify_signature)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jws.py\", line 70, in verify\n    header, payload, signing_input, signature = _load(token)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jws.py\", line 180, in _load\n    raise JWSError(\"Not enough segments\")\njose.exceptions.JWSError: Not enough segments\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/utils/authentication_manager/jwt_handler.py\", line 102, in decode_token\n    payload = jwt.decode(\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jwt.py\", line 144, in decode\n    raise JWTError(e)\njose.exceptions.JWTError: Not enough segments"}
{"timestamp": "2025-06-09T12:41:52.804849", "level": "ERROR", "message": "Failed to decode token", "module": "jwt_handler", "function": "decode_token", "line": 116, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jws.py\", line 176, in _load\n    signing_input, crypto_segment = jwt.rsplit(b\".\", 1)\nValueError: not enough values to unpack (expected 2, got 1)\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jwt.py\", line 142, in decode\n    payload = jws.verify(token, key, algorithms, verify=verify_signature)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jws.py\", line 70, in verify\n    header, payload, signing_input, signature = _load(token)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jws.py\", line 180, in _load\n    raise JWSError(\"Not enough segments\")\njose.exceptions.JWSError: Not enough segments\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/utils/authentication_manager/jwt_handler.py\", line 102, in decode_token\n    payload = jwt.decode(\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jwt.py\", line 144, in decode\n    raise JWTError(e)\njose.exceptions.JWTError: Not enough segments"}
{"timestamp": "2025-06-09T12:46:05.165592", "level": "ERROR", "message": "Request failed: POST /api/v1/auth/logout", "module": "logging_middleware", "function": "dispatch", "line": 33, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/base.py\", line 78, in call_next\n    message = await recv_stream.receive()\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/anyio/streams/memory.py\", line 98, in receive\n    return self.receive_nowait()\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/anyio/streams/memory.py\", line 91, in receive_nowait\n    raise EndOfStream\nanyio.EndOfStream\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/middleware/logging_middleware.py\", line 21, in dispatch\n    response = await call_next(request)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/base.py\", line 84, in call_next\n    raise app_exc\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/base.py\", line 70, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n    await self.simple_response(scope, receive, send, request_headers=headers)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n    raise exc\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n    await self.app(scope, receive, sender)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n    raise e\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/routing.py\", line 718, in __call__\n    await route.handle(scope, receive, send)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/routing.py\", line 276, in handle\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/routing.py\", line 66, in app\n    response = await func(request)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/fastapi/routing.py\", line 274, in app\n    raw_response = await run_endpoint_function(\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/fastapi/routing.py\", line 191, in run_endpoint_function\n    return await dependant.call(**values)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/api/v1/auth.py\", line 98, in logout_user\n    return await auth_operations.logout_user(str(current_user.id), language)\nAttributeError: 'dict' object has no attribute 'id'"}
{"timestamp": "2025-06-09T12:56:58.509570", "level": "ERROR", "message": "Failed to decode token", "module": "jwt_handler", "function": "decode_token", "line": 116, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jws.py\", line 176, in _load\n    signing_input, crypto_segment = jwt.rsplit(b\".\", 1)\nValueError: not enough values to unpack (expected 2, got 1)\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jwt.py\", line 142, in decode\n    payload = jws.verify(token, key, algorithms, verify=verify_signature)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jws.py\", line 70, in verify\n    header, payload, signing_input, signature = _load(token)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jws.py\", line 180, in _load\n    raise JWSError(\"Not enough segments\")\njose.exceptions.JWSError: Not enough segments\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/utils/authentication_manager/jwt_handler.py\", line 102, in decode_token\n    payload = jwt.decode(\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/jose/jwt.py\", line 144, in decode\n    raise JWTError(e)\njose.exceptions.JWTError: Not enough segments"}
{"timestamp": "2025-06-09T13:10:21.140655", "level": "ERROR", "message": "Request failed: POST /api/v1/auth/logout", "module": "logging_middleware", "function": "dispatch", "line": 33, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/base.py\", line 78, in call_next\n    message = await recv_stream.receive()\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/anyio/streams/memory.py\", line 98, in receive\n    return self.receive_nowait()\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/anyio/streams/memory.py\", line 91, in receive_nowait\n    raise EndOfStream\nanyio.EndOfStream\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/core/middleware/logging_middleware.py\", line 21, in dispatch\n    response = await call_next(request)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/base.py\", line 84, in call_next\n    raise app_exc\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/base.py\", line 70, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n    await self.simple_response(scope, receive, send, request_headers=headers)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n    raise exc\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n    await self.app(scope, receive, sender)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n    raise e\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/routing.py\", line 718, in __call__\n    await route.handle(scope, receive, send)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/routing.py\", line 276, in handle\n    await self.app(scope, receive, send)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/starlette/routing.py\", line 66, in app\n    response = await func(request)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/fastapi/routing.py\", line 274, in app\n    raw_response = await run_endpoint_function(\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/venv/lib/python3.9/site-packages/fastapi/routing.py\", line 191, in run_endpoint_function\n    return await dependant.call(**values)\n  File \"/Users/<USER>/Projects/Python Projects/GROWTH-HIVE/app/api/v1/auth.py\", line 98, in logout_user\n    return await auth_operations.logout_user(str(current_user.id), language)\nAttributeError: 'dict' object has no attribute 'id'"}
