from pydantic_settings import BaseSettings
from pydantic import Field
from typing import List, Optional
from functools import lru_cache


class Settings(BaseSettings):
    # Database
    DATABASE_HOST: str = Field(default="localhost", description="Database host")
    DATABASE_PORT: int = Field(default=5432, description="Database port") 
    DATABASE_USER: str = Field(default="postgres", description="Database user")
    DATABASE_PASSWORD: str = Field(default="root", description="Database password")
    DATABASE_NAME: str = Field(default="GrowthHive", description="Database name")
    
    # JWT Configuration
    JWT_SECRET_KEY: str = Field(default="your-secret-key-change-in-production", description="JWT secret key")
    JWT_ALGORITHM: str = Field(default="HS256", description="JWT algorithm")
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, description="JWT access token expire minutes")
    JWT_LONG_TOKEN_EXPIRE_DAYS: int = Field(default=30, description="JWT long token expire days")
    
    # Application
    APP_NAME: str = Field(default="Growth Hive Auth API", description="Application name")
    DEBUG: bool = Field(default=True, description="Debug mode")
    
    # CORS
    CORS_ORIGINS: List[str] = Field(default=["*"], description="CORS origins")
    CORS_CREDENTIALS: bool = Field(default=True, description="CORS credentials")
    CORS_METHODS: List[str] = Field(default=["*"], description="CORS methods")
    CORS_HEADERS: List[str] = Field(default=["*"], description="CORS headers")
    
    @property
    def database_url(self) -> str:
        return f"postgresql+asyncpg://{self.DATABASE_USER}:{self.DATABASE_PASSWORD}@{self.DATABASE_HOST}:{self.DATABASE_PORT}/{self.DATABASE_NAME}"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    return Settings()

# Create settings instance
settings = get_settings() 