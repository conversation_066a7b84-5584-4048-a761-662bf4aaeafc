#!/usr/bin/env python3
"""
Robust AutoGen Team Runner with Enhanced <PERSON><PERSON><PERSON> Handling
Handles validation errors gracefully and provides detailed progress tracking
"""

import asyncio
import os
import json
import time
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional
from dotenv import load_dotenv
from autogen_core import ComponentLoader
from autogen_agentchat.ui import Console

class ConfigValidator:
    """Validates and fixes AutoGen configuration files"""
    
    @staticmethod
    def validate_agent_config(agent_config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and fix agent configuration"""
        required_fields = {
            'provider': 'autogen_agentchat.agents.AssistantAgent',
            'component_type': 'agent',
            'version': 1,
            'component_version': 1,
            'description': 'AI Agent',
            'label': 'Agent'
        }
        
        # Add missing top-level fields
        for field, default_value in required_fields.items():
            if field not in agent_config:
                agent_config[field] = default_value
                print(f"⚠️  Added missing field '{field}' with default value")
        
        # Validate config section
        if 'config' in agent_config:
            config = agent_config['config']
            
            # Required config fields
            config_required = {
                'model_client_stream': False,
                'reflect_on_tool_use': False,
                'tool_call_summary_format': '{result}',
                'metadata': {}
            }
            
            for field, default_value in config_required.items():
                if field not in config:
                    config[field] = default_value
                    print(f"⚠️  Added missing config field '{field}'")
            
            # Validate model_client
            if 'model_client' in config:
                model_client = config['model_client']
                model_required = {
                    'component_type': 'model',
                    'version': 1,
                    'component_version': 1,
                    'description': 'Chat completion client for OpenAI hosted models.',
                    'label': 'OpenAIChatCompletionClient'
                }
                
                for field, default_value in model_required.items():
                    if field not in model_client:
                        model_client[field] = default_value
                        print(f"⚠️  Added missing model_client field '{field}'")
            
            # Validate model_context
            if 'model_context' in config:
                context = config['model_context']
                context_required = {
                    'component_type': 'chat_completion_context',
                    'version': 1,
                    'component_version': 1,
                    'description': 'An unbounded chat completion context that keeps a view of all messages.',
                    'label': 'UnboundedChatCompletionContext'
                }
                
                for field, default_value in context_required.items():
                    if field not in context:
                        context[field] = default_value
                        print(f"⚠️  Added missing model_context field '{field}'")
        
        return agent_config
    
    @staticmethod
    def validate_config_file(config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate entire configuration file"""
        print("🔍 Validating configuration file...")
        
        # Validate participants
        if 'config' in config_data and 'participants' in config_data['config']:
            participants = config_data['config']['participants']
            for i, participant in enumerate(participants):
                print(f"🔍 Validating participant {i+1}: {participant.get('label', 'Unknown')}")
                participants[i] = ConfigValidator.validate_agent_config(participant)
        
        print("✅ Configuration validation completed")
        return config_data

class RobustProgressTracker:
    """Enhanced progress tracker with error handling"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.messages_count = 0
        self.errors_count = 0
        self.current_agent = None
        self.conversation_log = []
        
    def log_message(self, agent_name: str, content: str, message_type: str = "normal"):
        """Log a message with enhanced tracking"""
        self.messages_count += 1
        self.current_agent = agent_name
        
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'agent': agent_name,
            'content': content[:500] + "..." if len(content) > 500 else content,
            'type': message_type,
            'message_id': self.messages_count
        }
        self.conversation_log.append(log_entry)
        
        # Display progress
        elapsed = datetime.now() - self.start_time
        print(f"\n{'='*60}")
        print(f"📊 MESSAGE #{self.messages_count} | {message_type.upper()}")
        print(f"⏱️  Elapsed: {elapsed}")
        print(f"🤖 Agent: {agent_name}")
        print(f"💬 Content: {content[:100]}...")
        print(f"{'='*60}\n")
        
        # Save log periodically
        if self.messages_count % 5 == 0:
            self.save_log()
    
    def log_error(self, error: Exception, context: str = ""):
        """Log an error"""
        self.errors_count += 1
        error_msg = f"Error in {context}: {str(error)}"
        self.log_message("SYSTEM", error_msg, "error")
        print(f"❌ ERROR #{self.errors_count}: {error_msg}")
    
    def save_log(self):
        """Save conversation log"""
        try:
            with open('robust_autogen_log.json', 'w') as f:
                json.dump({
                    'session_info': {
                        'start_time': self.start_time.isoformat(),
                        'messages_count': self.messages_count,
                        'errors_count': self.errors_count,
                        'duration': str(datetime.now() - self.start_time)
                    },
                    'conversation': self.conversation_log
                }, f, indent=2)
        except Exception as e:
            print(f"⚠️  Warning: Could not save log: {e}")

class RobustConsole:
    """Enhanced console with error handling"""
    
    def __init__(self, progress_tracker: RobustProgressTracker):
        self.progress_tracker = progress_tracker
        
    async def __call__(self, stream):
        """Process conversation stream with robust error handling"""
        print("🎬 Starting robust conversation stream...")
        
        try:
            async for message in stream:
                try:
                    # Extract message information safely
                    agent_name = getattr(message, 'source', 'Unknown')
                    content = str(message.content) if hasattr(message, 'content') else str(message)
                    
                    # Log the message
                    self.progress_tracker.log_message(agent_name, content)
                    
                    # Display formatted message
                    print(f"🤖 {agent_name}:")
                    print(f"   {content}")
                    print("-" * 40)
                    
                except Exception as e:
                    self.progress_tracker.log_error(e, "message processing")
                    continue
                    
        except Exception as e:
            self.progress_tracker.log_error(e, "stream processing")
            print("❌ Stream processing failed, but continuing...")
        
        print("✅ Conversation stream completed!")
        self.progress_tracker.save_log()

async def load_and_run_team_robust():
    """Load and run team with robust error handling"""
    
    # Load environment variables
    load_dotenv()
    
    # Initialize tracker
    progress_tracker = RobustProgressTracker()
    console = RobustConsole(progress_tracker)
    
    try:
        print("🚀 Robust AutoGen Team Runner")
        print("=" * 60)
        
        # Check API key
        if not os.getenv("OPENAI_API_KEY"):
            print("❌ OPENAI_API_KEY not found in environment variables")
            return
        
        print("✅ OpenAI API key configured")
        
        # Load configuration file
        config_files = [
            "ai_dev_team_config_with_augment_planner.json",
            "ai_dev_team_config.json"
        ]
        
        config_file = None
        for file in config_files:
            if os.path.exists(file):
                config_file = file
                break
        
        if not config_file:
            print(f"❌ No configuration file found! Looked for: {config_files}")
            return
        
        print(f"📋 Loading configuration from: {config_file}")
        
        # Load and validate configuration
        try:
            with open(config_file, "r", encoding="utf-8") as f:
                config_data = json.load(f)
            
            # Validate and fix configuration
            config_data = ConfigValidator.validate_config_file(config_data)
            
        except json.JSONDecodeError as e:
            print(f"❌ Invalid JSON in configuration file: {e}")
            return
        except Exception as e:
            progress_tracker.log_error(e, "configuration loading")
            return
        
        # Load team
        try:
            loader = ComponentLoader()
            team = loader.load_component(config_data)
            print("✅ AI Development Team loaded successfully!")
            
        except Exception as e:
            progress_tracker.log_error(e, "team loading")
            print(f"❌ Failed to load team: {e}")
            print("💡 Suggestion: Check the configuration file format and required fields")
            return
        
        # Define task
        task = """
        Build a comprehensive GrowthHive backend system with the following requirements:

        🎯 CORE FEATURES:
        1. FastAPI backend with comprehensive API endpoints
        2. Supabase integration with PostgreSQL and vector support
        3. RAG (Retrieval-Augmented Generation) system for document analysis
        4. SMS integration for prospect outreach (Twilio/Kudocity)
        5. Zoho CRM integration for lead management
        6. Vector database for intelligent document search
        7. Authentication and authorization system
        8. Real-time notifications and updates

        🏗️ ARCHITECTURE REQUIREMENTS:
        - Microservices architecture with clear separation of concerns
        - Scalable and maintainable code structure
        - Comprehensive error handling and logging
        - API documentation with OpenAPI/Swagger
        - Database migrations and schema management
        - Testing suite with unit and integration tests
        - Docker containerization for deployment

        Please start with the augment_planner to break down this task into manageable subtasks,
        then proceed with systematic implementation by the appropriate specialists.
        """
        
        print("🎯 Starting AI Development Team...")
        print("=" * 80)
        
        # Run team with robust error handling
        try:
            stream = team.run_stream(task=task)
            await console(stream)
            
        except Exception as e:
            progress_tracker.log_error(e, "team execution")
            print(f"❌ Team execution failed: {e}")
        
        # Final summary
        print("=" * 80)
        print("🏁 Session Summary:")
        print(f"📊 Messages processed: {progress_tracker.messages_count}")
        print(f"❌ Errors encountered: {progress_tracker.errors_count}")
        print(f"⏱️  Total time: {datetime.now() - progress_tracker.start_time}")
        print(f"📄 Log saved to: robust_autogen_log.json")
        print("=" * 80)
        
    except Exception as e:
        progress_tracker.log_error(e, "main execution")
        print(f"❌ Fatal error: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main entry point"""
    print("🛡️  Robust AutoGen Team Runner")
    print("Enhanced error handling and validation")
    print("=" * 60)
    
    try:
        asyncio.run(load_and_run_team_robust())
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"❌ Fatal error: {e}")

if __name__ == "__main__":
    main()
