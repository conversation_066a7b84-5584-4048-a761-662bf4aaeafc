from typing import Optional, Union, List, Dict, Any
from pydantic import BaseModel, Field, model_validator


class PaginationModel(BaseModel):
    """Model for pagination details in responses"""
    current_page: int = Field(..., description="Current page number")
    total_pages: int = Field(..., description="Total number of pages")
    items_per_page: int = Field(..., description="Number of items per page")
    total_items: int = Field(..., description="Total number of items")


class MessageModel(BaseModel):
    """Model for response messages"""
    title: str = Field(..., description="Message title")
    description: str = Field(..., description="Detailed message description")


class DataModel(BaseModel):
    """Model for response data"""
    details: Optional[Union[Dict[str, Any], List[Any]]] = Field(
        default=None,
        description="Response data details"
    )
    pagination: Optional[PaginationModel] = Field(
        default=None,
        description="Pagination information if applicable"
    )


class ResponseModel(BaseModel):
    """Base response model for all API responses"""
    success: bool = Field(..., description="Whether the request was successful")
    message: MessageModel = Field(..., description="Response message")
    data: DataModel = Field(
        default_factory=DataModel,
        description="Response data",
        exclude=False
    )
    error_code: int = Field(
        default=0,
        description="Error code (0 for success)"
    )

    @model_validator(mode="after")
    def validate_error_code(self) -> "ResponseModel":
        """Validate that error code is 0 for successful responses"""
        if self.success and self.error_code != 0:
            raise ValueError("Error code must be 0 for successful responses")
        return self


class WorkingHourEntry(BaseModel):
    """Model for working hours entry"""
    day_of_week: str = Field(..., description="Day of the week")
    start_time: Optional[str] = Field(
        default=None,
        description="Start time in HH:MM format"
    )
    end_time: Optional[str] = Field(
        default=None,
        description="End time in HH:MM format"
    )
    is_24_hours: bool = Field(
        default=False,
        description="Whether the business is open 24 hours"
    )


class EmergencyHourEntry(BaseModel):
    """Model for emergency hours entry"""
    day_of_week: str = Field(..., description="Day of the week")
    start_time: Optional[str] = Field(
        default=None,
        description="Start time in HH:MM format"
    )
    end_time: Optional[str] = Field(
        default=None,
        description="End time in HH:MM format"
    )
    is_24_hours: bool = Field(
        default=False,
        description="Whether emergency service is available 24 hours"
    )
    cost: Optional[float] = Field(
        default=None,
        description="Emergency service cost"
    )


class ServiceAreaDetailsUpdateRequest(BaseModel):
    """Model for service area details update request"""
    maintenance_capacity: Optional[int] = Field(
        default=None,
        description="Maintenance service capacity"
    )
    turnover_capacity: Optional[int] = Field(
        default=None,
        description="Turnover service capacity"
    )
    initial_rehab_capacity: Optional[int] = Field(
        default=None,
        description="Initial rehabilitation service capacity"
    )
    total_service_capacity: Optional[int] = Field(
        default=None,
        description="Total service capacity"
    )
    service_types: List[int] = Field(
        default_factory=list,
        description="List of service type IDs"
    )
    inherit_global_working_hours: bool = Field(
        ...,
        description="Whether to inherit global working hours"
    )
    same_for_all_days: Optional[bool] = Field(
        default=False,
        description="Whether working hours are same for all days"
    )
    standard_business_hours: Optional[List[WorkingHourEntry]] = Field(
        default=None,
        description="Standard business hours"
    )
    emergency_and_after_hours_coverage: Optional[bool] = Field(
        default=False,
        description="Whether emergency and after hours coverage is available"
    )
    emergency_working_hours: Optional[List[EmergencyHourEntry]] = Field(
        default=None,
        description="Emergency working hours"
    )
    vendor_remarks: Optional[str] = Field(
        default=None,
        description="Additional remarks from vendor"
    )

    @model_validator(mode="after")
    def check_emergency_conflict(self) -> "ServiceAreaDetailsUpdateRequest":
        """Validate that emergency hours don't conflict with standard hours"""
        if not self.inherit_global_working_hours and self.emergency_and_after_hours_coverage:
            std_hours = {h.day_of_week: h for h in self.standard_business_hours or []}
            for e in self.emergency_working_hours or []:
                std = std_hours.get(e.day_of_week)
                if std and not e.is_24_hours and not std.is_24_hours:
                    if (e.start_time and e.end_time and std.start_time and std.end_time and
                            not (e.end_time <= std.start_time or e.start_time >= std.end_time)):
                        raise ValueError(f"Emergency hours overlap with standard hours on {e.day_of_week}")
        return self
