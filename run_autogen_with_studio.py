#!/usr/bin/env python3
"""
AutoGen Team Runner with Studio Web Interface
Runs the AI development team from JSON config with AutoGen Studio web monitoring
"""

import asyncio
import os
import json
import time
import threading
import subprocess
import sys
from pathlib import Path
from datetime import datetime
from dotenv import load_dotenv
from autogen_core import ComponentLoader
from autogen_agentchat.ui import Console

class AutoGenStudioManager:
    """Manages AutoGen Studio web interface"""
    
    def __init__(self, port=8081):
        self.port = port
        self.studio_process = None
        self.studio_url = f"http://localhost:{port}"
        
    def start_studio(self):
        """Start AutoGen Studio web interface"""
        try:
            print(f"🌐 Starting AutoGen Studio on {self.studio_url}")
            
            # Start AutoGen Studio in a separate process
            cmd = [sys.executable, "-m", "autogenstudio.web.ui", "--port", str(self.port)]
            self.studio_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait a moment for startup
            time.sleep(3)
            
            if self.studio_process.poll() is None:
                print(f"✅ AutoGen Studio started successfully!")
                print(f"🔗 Access the web interface at: {self.studio_url}")
                return True
            else:
                print("❌ Failed to start AutoGen Studio")
                return False
                
        except Exception as e:
            print(f"❌ Error starting AutoGen Studio: {e}")
            return False
    
    def stop_studio(self):
        """Stop AutoGen Studio"""
        if self.studio_process:
            self.studio_process.terminate()
            self.studio_process.wait()
            print("🛑 AutoGen Studio stopped")

class ProgressTracker:
    """Tracks and displays development progress"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.messages_count = 0
        self.current_agent = None
        self.phases = [
            "Architecture Design",
            "Backend Development", 
            "Database Setup",
            "AI Integration",
            "Testing",
            "Deployment"
        ]
        self.current_phase_index = 0
        
    def update_progress(self, agent_name, message):
        """Update progress tracking"""
        self.messages_count += 1
        self.current_agent = agent_name
        
        # Display progress
        elapsed = datetime.now() - self.start_time
        print(f"\n{'='*60}")
        print(f"📊 PROGRESS UPDATE - Message #{self.messages_count}")
        print(f"⏱️  Elapsed Time: {elapsed}")
        print(f"🎯 Current Phase: {self.phases[self.current_phase_index]}")
        print(f"🤖 Active Agent: {agent_name}")
        print(f"💬 Latest: {message[:100]}...")
        print(f"{'='*60}\n")
        
    def advance_phase(self):
        """Move to next development phase"""
        if self.current_phase_index < len(self.phases) - 1:
            self.current_phase_index += 1
            print(f"🚀 Advancing to: {self.phases[self.current_phase_index]}")

class EnhancedConsole:
    """Enhanced console with progress tracking and web integration"""
    
    def __init__(self, progress_tracker, studio_manager):
        self.progress_tracker = progress_tracker
        self.studio_manager = studio_manager
        self.conversation_log = []
        
    async def __call__(self, stream):
        """Process the conversation stream with enhanced logging"""
        print("🎬 Starting conversation stream...")
        
        async for message in stream:
            # Track progress
            agent_name = getattr(message, 'source', 'Unknown')
            content = str(message.content) if hasattr(message, 'content') else str(message)
            
            self.progress_tracker.update_progress(agent_name, content)
            
            # Log conversation
            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'agent': agent_name,
                'content': content,
                'message_id': self.progress_tracker.messages_count
            }
            self.conversation_log.append(log_entry)
            
            # Display message with formatting
            print(f"🤖 {agent_name}:")
            print(f"   {content}")
            print("-" * 40)
            
            # Save conversation log periodically
            if len(self.conversation_log) % 5 == 0:
                self.save_conversation_log()
                
        print("✅ Conversation stream completed!")
        
    def save_conversation_log(self):
        """Save conversation log to file"""
        try:
            with open('autogen_conversation_log.json', 'w') as f:
                json.dump(self.conversation_log, f, indent=2)
        except Exception as e:
            print(f"⚠️  Warning: Could not save conversation log: {e}")

async def load_and_run_team():
    """Load team from JSON config and run with enhanced monitoring"""

    # Load environment variables
    load_dotenv()

    # Initialize managers
    studio_manager = AutoGenStudioManager()
    progress_tracker = ProgressTracker()
    enhanced_console = EnhancedConsole(progress_tracker, studio_manager)

    try:
        # Start AutoGen Studio (skip for now due to compatibility issues)
        print("🚀 Initializing AutoGen Studio...")
        print("⚠️  Skipping AutoGen Studio web interface (compatibility issues)")
        print("📊 Progress will be shown in console with enhanced tracking")
        studio_started = False

        # Set OpenAI API key
        if not os.getenv("OPENAI_API_KEY"):
            print("❌ OPENAI_API_KEY not found in environment variables")
            print("Please set your OpenAI API key:")
            api_key = input("Enter OpenAI API Key: ").strip()
            os.environ["OPENAI_API_KEY"] = api_key

        print("✅ OpenAI API key configured")

        # Load team configuration
        config_file = "ai_dev_team_config.json"
        if not os.path.exists(config_file):
            print(f"❌ Configuration file {config_file} not found!")
            return

        print(f"📋 Loading team configuration from {config_file}")

        # Load the team configuration from JSON file
        with open(config_file, "r", encoding="utf-8") as f:
            config_data = json.load(f)

        # Load the team using ComponentLoader
        loader = ComponentLoader()
        team = loader.load_component(config_data)
        
        print("✅ AI Development Team loaded successfully!")
        print(f"👥 Team: AI Application Development Team")

        # Define the development task
        task = """
        Build a comprehensive GrowthHive backend system with the following requirements:

        🎯 CORE FEATURES:
        1. FastAPI backend with comprehensive API endpoints
        2. Supabase integration with PostgreSQL and vector support
        3. RAG (Retrieval-Augmented Generation) system for document analysis
        4. SMS integration for prospect outreach
        5. Zoho CRM integration for lead management
        6. Vector database for intelligent document search
        7. Authentication and authorization system
        8. Real-time notifications and updates

        🏗️ ARCHITECTURE REQUIREMENTS:
        - Microservices architecture with clear separation of concerns
        - Scalable and maintainable code structure
        - Comprehensive error handling and logging
        - API documentation with OpenAPI/Swagger
        - Database migrations and schema management
        - Testing suite with unit and integration tests
        - Docker containerization for deployment

        📋 DEVELOPMENT PHASES:
        1. System architecture design and planning
        2. Database schema design with vector support
        3. FastAPI application structure and core endpoints
        4. Supabase integration and authentication
        5. RAG system implementation
        6. SMS and CRM integrations
        7. Testing and quality assurance
        8. Deployment configuration

        Please start with architecture design and proceed systematically through each phase.
        Focus on creating production-ready, scalable code with proper documentation.
        """
        
        print("🎯 Starting AI Development Team...")
        print("=" * 80)
        print("📝 TASK OVERVIEW:")
        print(task)
        print("=" * 80)
        
        # Run the team with enhanced console
        stream = team.run_stream(task=task)
        await enhanced_console(stream)
        
        print("=" * 80)
        print("✅ AI Development Team execution completed!")
        print(f"📊 Total messages processed: {progress_tracker.messages_count}")
        print(f"⏱️  Total time: {datetime.now() - progress_tracker.start_time}")
        print("📄 Conversation log saved to: autogen_conversation_log.json")

        print("� Development session completed successfully!")
        print("📝 Check the generated files and conversation log for details")
                
    except Exception as e:
        print(f"❌ Error running team: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Clean up
        studio_manager.stop_studio()
        print("🏁 Session ended")

def main():
    """Main entry point"""
    print("🚀 AutoGen Team Runner with Studio Web Interface")
    print("=" * 60)
    print("This script will:")
    print("1. Start AutoGen Studio web interface")
    print("2. Load AI development team from JSON config")
    print("3. Run the team with real-time progress tracking")
    print("4. Provide web monitoring at http://localhost:8081")
    print("=" * 60)
    
    try:
        asyncio.run(load_and_run_team())
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"❌ Fatal error: {e}")

if __name__ == "__main__":
    main()
