from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional
import re


class UserRegistrationRequest(BaseModel):
    """Schema for user registration request"""
    first_name: str = Field(..., min_length=1, max_length=100, description="User's first name")
    last_name: str = Field(..., min_length=1, max_length=100, description="User's last name")
    email: EmailStr = Field(..., description="User's email address")
    mobile_number: Optional[str] = Field(None, description="User's mobile number")
    password: str = Field(..., min_length=8, description="User's password")
    confirm_password: str = Field(..., description="Password confirmation")

    @validator('mobile_number')
    def validate_mobile_number(cls, v):
        if v is not None and not re.match(r'^\+?1?\d{10,15}$', v):
            raise ValueError('Invalid mobile number format')
        return v

    @validator('confirm_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'password' in values and v != values['password']:
            raise ValueError('Passwords do not match')
        return v

    class Config:
        schema_extra = {
            "example": {
                "first_name": "John",
                "last_name": "Doe",
                "email": "<EMAIL>",
                "mobile_number": "+1234567890",
                "password": "SecurePass123!",
                "confirm_password": "SecurePass123!"
            }
        }


class UserLoginRequest(BaseModel):
    """Schema for user login request"""
    identifier: str = Field(..., description="Email or mobile number")
    password: str = Field(..., description="User's password")
    remember_me: bool = Field(default=True, description="Remember me for extended session")

    class Config:
        schema_extra = {
            "example": {
                "identifier": "<EMAIL>",
                "password": "SecurePass123!",
                "remember_me": True
            }
        }


class ForgotPasswordRequest(BaseModel):
    """Schema for forgot password request"""
    identifier: str = Field(..., description="Email or mobile number")

    class Config:
        schema_extra = {
            "example": {
                "identifier": "<EMAIL>"
            }
        }


class ResetPasswordRequest(BaseModel):
    """Schema for reset password request"""
    token: str = Field(..., description="Password reset token")
    new_password: str = Field(..., min_length=8, description="New password")
    confirm_password: str = Field(..., description="Confirm new password")

    @validator('confirm_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('Passwords do not match')
        return v

    class Config:
        schema_extra = {
            "example": {
                "token": "reset-token-123",
                "new_password": "NewSecurePass123!",
                "confirm_password": "NewSecurePass123!"
            }
        }


class ChangePasswordRequest(BaseModel):
    """Schema for change password request"""
    current_password: str = Field(..., description="Current password")
    new_password: str = Field(..., min_length=8, description="New password")
    confirm_password: str = Field(..., description="Confirm new password")

    @validator('confirm_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('Passwords do not match')
        return v

    class Config:
        schema_extra = {
            "example": {
                "current_password": "OldPassword123!",
                "new_password": "NewSecurePass123!",
                "confirm_password": "NewSecurePass123!"
            }
        }


class UserResponse(BaseModel):
    """Schema for user response"""
    id: str = Field(..., description="User's unique ID")
    first_name: Optional[str] = Field(None, description="User's first name")
    last_name: Optional[str] = Field(None, description="User's last name")
    email: str = Field(..., description="User's email address")
    mobile_number: Optional[str] = Field(None, description="User's mobile number")
    role: str = Field(..., description="User's role")
    is_active: bool = Field(..., description="User's active status")
    email_verified_at: Optional[str] = Field(None, description="Email verified timestamp")
    last_login_at: Optional[str] = Field(None, description="Last login timestamp")
    remember_me_token: Optional[str] = Field(None, description="Remember me token")
    created_at: str = Field(..., description="User creation timestamp")
    updated_at: str = Field(..., description="User last update timestamp")

    class Config:
        schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-426614174000",
                "first_name": "John",
                "last_name": "Doe",
                "email": "<EMAIL>",
                "mobile_number": "+1234567890",
                "role": "admin",
                "is_active": True,
                "email_verified_at": "2024-01-01T00:00:00Z",
                "last_login_at": "2024-01-02T12:00:00Z",
                "remember_me_token": None,
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z"
            }
        }


class LoginResponse(BaseModel):
    """Schema for login response"""
    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field(default="bearer", description="Token type")
    user: UserResponse = Field(..., description="User information")

    class Config:
        schema_extra = {
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer",
                "user": {
                    "id": "123e4567-e89b-12d3-a456-426614174000",
                    "first_name": "John",
                    "last_name": "Doe",
                    "email": "<EMAIL>",
                    "mobile_number": "+1234567890",
                    "role": "admin",
                    "is_active": True,
                    "email_verified_at": "2024-01-01T00:00:00Z",
                    "last_login_at": "2024-01-02T12:00:00Z",
                    "remember_me_token": None,
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z"
                }
            }
        }


class MessageResponse(BaseModel):
    """Schema for message response"""
    title: str = Field(..., description="Response title")
    description: str = Field(..., description="Response description")


class StandardResponse(BaseModel):
    """Schema for standard API response"""
    success: bool = Field(..., description="Operation success status")
    message: MessageResponse = Field(..., description="Response message")
    data: Optional[dict] = Field(None, description="Response data")
    error_code: int = Field(default=0, description="Error code (0 for success)")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": {
                    "title": "Success",
                    "description": "Operation completed successfully"
                },
                "data": {},
                "error_code": 0
            }
        } 