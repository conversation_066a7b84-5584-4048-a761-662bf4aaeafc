"""Franchise management API endpoints"""
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from typing import List, Optional
from uuid import UUID
from pydantic import BaseModel
from app.core.database import get_db
from app.models.models import Franchise, Document
from app.api.v1.auth import get_current_user

router = APIRouter(prefix="/franchises", tags=["franchises"])

class FranchiseCreate(BaseModel):
    name: str
    category: str
    sub_category: Optional[str] = None
    region: str
    budget: Optional[float] = None
    zoho_id: Optional[str] = None

class FranchiseUpdate(BaseModel):
    name: Optional[str] = None
    category: Optional[str] = None
    sub_category: Optional[str] = None
    region: Optional[str] = None
    budget: Optional[float] = None
    zoho_id: Optional[str] = None

class FranchiseResponse(BaseModel):
    id: str
    name: str
    category: str
    sub_category: Optional[str]
    region: str
    budget: Optional[float]
    brochure_url: Optional[str]
    zoho_id: Optional[str]
    created_at: str
    updated_at: str

@router.get("/", response_model=List[FranchiseResponse])
async def list_franchises(
    skip: int = 0,
    limit: int = 100,
    category: Optional[str] = None,
    region: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """List all franchises with optional filtering"""
    query = select(Franchise)
    
    if category:
        query = query.where(Franchise.category == category)
    if region:
        query = query.where(Franchise.region == region)
    
    query = query.offset(skip).limit(limit)
    result = await db.execute(query)
    franchises = result.scalars().all()
    
    return [
        FranchiseResponse(
            id=str(franchise.id),
            name=franchise.name,
            category=franchise.category,
            sub_category=franchise.sub_category,
            region=franchise.region,
            budget=float(franchise.budget) if franchise.budget else None,
            brochure_url=franchise.brochure_url,
            zoho_id=franchise.zoho_id,
            created_at=franchise.created_at.isoformat(),
            updated_at=franchise.updated_at.isoformat()
        )
        for franchise in franchises
    ]

@router.post("/", response_model=FranchiseResponse)
async def create_franchise(
    franchise_data: FranchiseCreate,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Create a new franchise"""
    franchise = Franchise(**franchise_data.dict())
    db.add(franchise)
    await db.commit()
    await db.refresh(franchise)
    
    return FranchiseResponse(
        id=str(franchise.id),
        name=franchise.name,
        category=franchise.category,
        sub_category=franchise.sub_category,
        region=franchise.region,
        budget=float(franchise.budget) if franchise.budget else None,
        brochure_url=franchise.brochure_url,
        zoho_id=franchise.zoho_id,
        created_at=franchise.created_at.isoformat(),
        updated_at=franchise.updated_at.isoformat()
    )

@router.get("/{franchise_id}", response_model=FranchiseResponse)
async def get_franchise(
    franchise_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Get a specific franchise by ID"""
    result = await db.execute(select(Franchise).where(Franchise.id == franchise_id))
    franchise = result.scalar_one_or_none()
    
    if not franchise:
        raise HTTPException(status_code=404, detail="Franchise not found")
    
    return FranchiseResponse(
        id=str(franchise.id),
        name=franchise.name,
        category=franchise.category,
        sub_category=franchise.sub_category,
        region=franchise.region,
        budget=float(franchise.budget) if franchise.budget else None,
        brochure_url=franchise.brochure_url,
        zoho_id=franchise.zoho_id,
        created_at=franchise.created_at.isoformat(),
        updated_at=franchise.updated_at.isoformat()
    )

@router.put("/{franchise_id}", response_model=FranchiseResponse)
async def update_franchise(
    franchise_id: UUID,
    franchise_data: FranchiseUpdate,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Update a franchise"""
    result = await db.execute(select(Franchise).where(Franchise.id == franchise_id))
    franchise = result.scalar_one_or_none()
    
    if not franchise:
        raise HTTPException(status_code=404, detail="Franchise not found")
    
    # Update fields
    for field, value in franchise_data.dict(exclude_unset=True).items():
        setattr(franchise, field, value)
    
    await db.commit()
    await db.refresh(franchise)
    
    return FranchiseResponse(
        id=str(franchise.id),
        name=franchise.name,
        category=franchise.category,
        sub_category=franchise.sub_category,
        region=franchise.region,
        budget=float(franchise.budget) if franchise.budget else None,
        brochure_url=franchise.brochure_url,
        zoho_id=franchise.zoho_id,
        created_at=franchise.created_at.isoformat(),
        updated_at=franchise.updated_at.isoformat()
    )

@router.delete("/{franchise_id}")
async def delete_franchise(
    franchise_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Delete a franchise"""
    result = await db.execute(select(Franchise).where(Franchise.id == franchise_id))
    franchise = result.scalar_one_or_none()
    
    if not franchise:
        raise HTTPException(status_code=404, detail="Franchise not found")
    
    await db.execute(delete(Franchise).where(Franchise.id == franchise_id))
    await db.commit()
    
    return {"message": "Franchise deleted successfully"}

@router.post("/{franchise_id}/upload-brochure")
async def upload_brochure(
    franchise_id: UUID,
    file: UploadFile = File(...),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Upload franchise brochure"""
    result = await db.execute(select(Franchise).where(Franchise.id == franchise_id))
    franchise = result.scalar_one_or_none()
    
    if not franchise:
        raise HTTPException(status_code=404, detail="Franchise not found")
    
    # Save file (implement file storage logic)
    file_path = f"uploads/brochures/{franchise_id}_{file.filename}"
    # TODO: Implement actual file saving logic
    
    # Update franchise with brochure URL
    franchise.brochure_url = file_path
    await db.commit()
    
    return {"message": "Brochure uploaded successfully", "file_path": file_path}
