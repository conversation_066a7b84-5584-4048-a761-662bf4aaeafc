#!/usr/bin/env python3
"""
Suggestion Listener - Accepts user suggestions during development
Runs in a separate terminal to capture real-time feedback
"""

import json
import os
from datetime import datetime
from pathlib import Path

class SuggestionListener:
    def __init__(self):
        self.suggestions_file = "user_suggestions.json"
        self.suggestions = []
        self.load_existing_suggestions()
    
    def load_existing_suggestions(self):
        """Load existing suggestions from file"""
        if os.path.exists(self.suggestions_file):
            try:
                with open(self.suggestions_file, 'r') as f:
                    self.suggestions = json.load(f)
            except:
                self.suggestions = []
    
    def save_suggestion(self, suggestion_text, priority="normal"):
        """Save a new suggestion"""
        suggestion = {
            "id": len(self.suggestions) + 1,
            "timestamp": datetime.now().isoformat(),
            "suggestion": suggestion_text,
            "priority": priority,
            "status": "pending",
            "implemented": False
        }
        
        self.suggestions.append(suggestion)
        
        # Save to file
        with open(self.suggestions_file, 'w') as f:
            json.dump(self.suggestions, f, indent=2)
        
        print(f"✅ Suggestion #{suggestion['id']} saved: {suggestion_text}")
        return suggestion['id']
    
    def display_suggestions(self):
        """Display all suggestions"""
        if not self.suggestions:
            print("📝 No suggestions yet.")
            return
        
        print("\n📋 CURRENT SUGGESTIONS:")
        print("-" * 60)
        for suggestion in self.suggestions:
            status_icon = "✅" if suggestion['implemented'] else "⏳"
            priority_icon = "🔥" if suggestion['priority'] == "high" else "📝"
            print(f"{status_icon} {priority_icon} #{suggestion['id']}: {suggestion['suggestion']}")
            print(f"   Time: {suggestion['timestamp'][:19]} | Status: {suggestion['status']}")
        print("-" * 60)
    
    def mark_implemented(self, suggestion_id):
        """Mark a suggestion as implemented"""
        for suggestion in self.suggestions:
            if suggestion['id'] == suggestion_id:
                suggestion['implemented'] = True
                suggestion['status'] = "implemented"
                break
        
        with open(self.suggestions_file, 'w') as f:
            json.dump(self.suggestions, f, indent=2)
    
    def run_listener(self):
        """Main listener loop"""
        print("🎯 GROWTHHIVE DEVELOPMENT - SUGGESTION LISTENER")
        print("=" * 60)
        print("💡 This terminal accepts your suggestions during development")
        print("📝 The AI team will implement your feedback in real-time")
        print("=" * 60)
        print("\nCommands:")
        print("  - Type your suggestion and press Enter")
        print("  - 'high: <suggestion>' for high priority")
        print("  - 'list' to see all suggestions")
        print("  - 'clear' to clear all suggestions")
        print("  - 'quit' to exit")
        print("\n" + "=" * 60)
        
        while True:
            try:
                user_input = input("\n💬 Your suggestion: ").strip()
                
                if not user_input:
                    continue
                
                if user_input.lower() == 'quit':
                    print("👋 Suggestion listener stopped.")
                    break
                
                elif user_input.lower() == 'list':
                    self.display_suggestions()
                
                elif user_input.lower() == 'clear':
                    self.suggestions = []
                    with open(self.suggestions_file, 'w') as f:
                        json.dump([], f)
                    print("🗑️ All suggestions cleared.")
                
                elif user_input.lower().startswith('high:'):
                    suggestion_text = user_input[5:].strip()
                    if suggestion_text:
                        self.save_suggestion(suggestion_text, "high")
                        print("🔥 HIGH PRIORITY suggestion saved!")
                
                elif user_input.lower().startswith('done:'):
                    try:
                        suggestion_id = int(user_input[5:].strip())
                        self.mark_implemented(suggestion_id)
                        print(f"✅ Suggestion #{suggestion_id} marked as implemented!")
                    except ValueError:
                        print("❌ Invalid suggestion ID. Use: done: <number>")
                
                else:
                    self.save_suggestion(user_input)
                    print("📝 Suggestion will be implemented by the AI team!")
                
            except KeyboardInterrupt:
                print("\n👋 Suggestion listener stopped.")
                break
            except Exception as e:
                print(f"❌ Error: {e}")

def main():
    """Main function"""
    listener = SuggestionListener()
    listener.run_listener()

if __name__ == "__main__":
    main()
