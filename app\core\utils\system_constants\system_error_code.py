# Predefined error codes with HTTP status codes and messages

pre_define_error = {
    "en": {
        # Authentication errors (4000-4099)
        "invalid_user_credentials": {
            "code": 4001,
            "http_status": 401,
            "message": {
                "title": "Invalid Credentials",
                "description": "The provided credentials are invalid. Please check your email and password."
            }
        },
        "account_disabled": {
            "code": 4002,
            "http_status": 403,
            "message": {
                "title": "Account Disabled",
                "description": "Your account has been disabled. Please contact support for assistance."
            }
        },
        "expired_access_token": {
            "code": 4003,
            "http_status": 401,
            "message": {
                "title": "Token Expired",
                "description": "Your access token has expired. Please login again to continue."
            }
        },
        "invalid_access_token": {
            "code": 4004,
            "http_status": 401,
            "message": {
                "title": "Invalid Token",
                "description": "The provided access token is invalid. Please login again to continue."
            }
        },
        "invalid_reset_token": {
            "code": 4005,
            "http_status": 400,
            "message": {
                "title": "Invalid Reset Token",
                "description": "The password reset token is invalid or expired. Please request a new reset link."
            }
        },
        
        # User errors (4100-4199)
        "user_not_found": {
            "code": 4101,
            "http_status": 404,
            "message": {
                "title": "User Not Found",
                "description": "The specified user was not found. Please check the user ID and try again."
            }
        },
        "email_already_exists": {
            "code": 4102,
            "http_status": 409,
            "message": {
                "title": "Email Already Exists",
                "description": "An account with this email already exists. Please use a different email address."
            }
        },
        "mobile_already_exists": {
            "code": 4103,
            "http_status": 409,
            "message": {
                "title": "Mobile Number Already Exists",
                "description": "An account with this mobile number already exists. Please use a different mobile number."
            }
        },
        "user_creation_failed": {
            "code": 4104,
            "http_status": 500,
            "message": {
                "title": "User Creation Failed",
                "description": "Failed to create user account. Please try again or contact support if the issue persists."
            }
        },
        
        # Validation errors (4200-4299)
        "password_policy_violation": {
            "code": 4201,
            "http_status": 400,
            "message": {
                "title": "Password Policy Violation",
                "description": "Password does not meet the required criteria. Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character."
            }
        },
        "invalid_input": {
            "code": 4202,
            "http_status": 400,
            "message": {
                "title": "Invalid Input",
                "description": "The provided input is invalid. Please check the input data and try again."
            }
        },
        
        # Server errors (5000-5099)
        "unknown_error": {
            "code": 5000,
            "http_status": 500,
            "message": {
                "title": "Internal Server Error",
                "description": "An unexpected error occurred. Please try again or contact support if the issue persists."
            }
        },
        "database_error": {
            "code": 5001,
            "http_status": 500,
            "message": {
                "title": "Database Error",
                "description": "A database error occurred. Please try again or contact support if the issue persists."
            }
        },
        "token_generation_error": {
            "code": 5002,
            "http_status": 500,
            "message": {
                "title": "Token Generation Error",
                "description": "Failed to generate authentication token. Please try again or contact support if the issue persists."
            }
        },
        
        # Vendor specific errors (5003-5199)
        "vendor_not_found": {
            "code": 5005,
            "http_status": 404,
            "message": {
                "title": "Vendor Not Found",
                "description": "The specified vendor was not found. Please check the vendor ID and try again."
            }
        },
        "vendor_profile_not_found": {
            "code": 5006,
            "http_status": 404,
            "message": {
                "title": "Vendor Profile Not Found",
                "description": "No vendor profile found for the given user. Please complete your vendor profile setup."
            }
        },
        "vendor_profile_fetch_error": {
            "code": 5007,
            "http_status": 500,
            "message": {
                "title": "Vendor Profile Fetch Error",
                "description": "An error occurred while fetching vendor profile. Please try again or contact support if the issue persists."
            }
        },
        "vendor_relation_not_found": {
            "code": 5008,
            "http_status": 404,
            "message": {
                "title": "Vendor Relation Not Found",
                "description": "The specified vendor relation was not found. Please check the relation ID and try again."
            }
        },
        
        # Business details errors (6000-6199)
        "business_details_save_error": {
            "code": 6000,
            "http_status": 500,
            "message": {
                "title": "Business Details Save Error",
                "description": "An error occurred while saving business details. Please try again or contact support if the issue persists."
            }
        },
        "business_details_not_found": {
            "code": 6001,
            "http_status": 404,
            "message": {
                "title": "Business Details Not Found",
                "description": "No business details found for this user. Please complete your business profile setup."
            }
        },
        
        # Location errors (7000-7099)
        "location_not_found": {
            "code": 7000,
            "http_status": 404,
            "message": {
                "title": "Location Not Found",
                "description": "The specified location was not found. Please check the location ID and try again."
            }
        },
        
        # Service area errors (3100-3199)
        "service_area_not_found": {
            "code": 3105,
            "http_status": 404,
            "message": {
                "title": "Service Area Not Found",
                "description": "The specified service area was not found. Please check the service area ID and try again."
            }
        },
        "profile_section_not_found": {
            "code": 3101,
            "http_status": 404,
            "message": {
                "title": "Profile Section Not Found",
                "description": "The specified profile section was not found. Please check the section ID and try again."
            }
        },
        "profile_status_update_failed": {
            "code": 3201,
            "http_status": 500,
            "message": {
                "title": "Profile Status Update Failed",
                "description": "Failed to update the vendor profile status"
            }
        },
        
        # General validation errors (4000-4099)
        "invalid_input_data": {
            "code": 4000,
            "http_status": 400,
            "message": {
                "title": "Invalid Input",
                "description": "The input data is invalid"
            }
        },
        
        # Service area specific errors (6001-6099)
        "service_area_save_error": {
            "code": 6001,
            "http_status": 500,
            "message": {
                "title": "Service Area Save Error",
                "description": "An error occurred while saving the service area"
            }
        },
        "service_area_list_error": {
            "code": 6003,
            "http_status": 500,
            "message": {
                "title": "Service Area List Error",
                "description": "An error occurred while fetching the service area list"
            }
        },
        "service_area_update_error": {
            "code": 6004,
            "http_status": 500,
            "message": {
                "title": "Service Area Update Error",
                "description": "An error occurred while updating the service area"
            }
        },
        
        # Verification errors (4300-4399)
        "user_not_verified": {
            "code": 4300,
            "http_status": 403,
            "message": {
                "title": "User Not Verified",
                "description": "User account is not verified"
            }
        }
    }
} 