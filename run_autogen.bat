@echo off
echo ========================================
echo AutoGen Team Runner with Studio
echo ========================================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Install/upgrade requirements
echo Installing requirements...
pip install -r autogen_requirements.txt

REM Check if .env file exists
if not exist ".env" (
    echo WARNING: .env file not found
    echo Please create .env file with your OpenAI API key
    echo Example: OPENAI_API_KEY=your_key_here
    pause
)

REM Run the AutoGen team with studio
echo Starting AutoGen Team with Studio...
echo Web interface will be available at: http://localhost:8081
echo.
python run_autogen_with_studio.py

echo.
echo Session ended. Press any key to exit...
pause
