"""SMS integration service using Twilio"""
from twilio.rest import Client
from app.core.settings import settings
from app.models.models import SMSConversation, Lead
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any

class SMSService:
    def __init__(self):
        self.client = Client(settings.twilio_account_sid, settings.twilio_auth_token)
        self.from_number = settings.sms_from_number

    async def send_sms(self, to_number: str, message: str, lead_id: str = None) -> Dict[str, Any]:
        """Send SMS message"""
        try:
            message_obj = self.client.messages.create(
                body=message,
                from_=self.from_number,
                to=to_number
            )

            return {
                "success": True,
                "message_sid": message_obj.sid,
                "status": message_obj.status
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    async def handle_webhook(self, webhook_data: Dict[str, Any], db: AsyncSession):
        """Handle incoming SMS webhook"""
        try:
            from_number = webhook_data.get("From")
            message_body = webhook_data.get("Body")

            # Find lead by phone number
            from sqlalchemy import select
            result = await db.execute(select(Lead).where(Lead.contact_number == from_number))
            lead = result.scalar_one_or_none()

            if lead:
                # Store incoming message
                sms_record = SMSConversation(
                    lead_id=lead.id,
                    message_content=message_body,
                    direction="inbound",
                    sms_service_id=webhook_data.get("MessageSid")
                )
                db.add(sms_record)

                # Generate AI response
                from app.ai.ai_service import AIService
                ai_service = AIService()
                response = await ai_service.generate_response(message_body, {
                    "name": lead.full_name,
                    "location": lead.location,
                    "franchise_preference": lead.franchise_preference
                })

                # Send response
                await self.send_sms(from_number, response, str(lead.id))

                # Store outbound message
                outbound_record = SMSConversation(
                    lead_id=lead.id,
                    message_content=response,
                    direction="outbound"
                )
                db.add(outbound_record)

                await db.commit()

        except Exception as e:
            print(f"SMS webhook error: {e}")
