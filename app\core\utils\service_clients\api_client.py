import json
from typing import Dict, Any, Optional

import requests
from requests.exceptions import RequestEx<PERSON>, Timeout, ConnectionError, HTTPError

from app import app_logger
from app.core.utils.exception_manager.custom_exceptions import (
    ValidationError,
    AuthenticationError,
    ResourceNotFoundError,
    BusinessLogicError,
    DatabaseError
)
from app.core.utils.exception_manager.exception_handler import exception_handler_obj


class APIClient:
    """
    A base class for making HTTP API calls with proper error handling and logging.
    """

    def __init__(self, base_url: str, timeout: int = 30):
        """
        Initialize the API client.
        
        Args:
            base_url: Base URL for the API
            timeout: Request timeout in seconds
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()

    async def _make_request(
            self,
            method: str,
            endpoint: str,
            data: Optional[Dict[str, Any]] = None,
            params: Optional[Dict[str, Any]] = None,
            headers: Optional[Dict[str, str]] = None,
            timeout: Optional[int] = None,
            language: str = "en"
    ) -> Dict[str, Any]:
        """
        Make an HTTP request with proper error handling.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint path
            data: Request body data
            params: URL query parameters
            headers: Request headers
            timeout: Request timeout in seconds
            language: Preferred language for error messages
            
        Returns:
            Dict containing the response data
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        timeout = timeout or self.timeout

        try:
            # Make the request
            response = self.session.request(
                method=method,
                url=url,
                json=data,
                params=params,
                headers=headers,
                timeout=timeout
            )

            # Log the request
            await app_logger.info(
                message=f"API Request: {method} {url}",
                function="_make_request"
            )

            # Raise for bad status codes
            response.raise_for_status()

            # Parse and return response
            return response.json()

        except Timeout:
            await app_logger.error(
                message_key="service_unavailable",
                function="_make_request",
                extra_message=f"Request timed out after {timeout} seconds"
            )
            raise BusinessLogicError(
                "service_unavailable",
                details={"timeout": timeout}
            )

        except ConnectionError:
            await app_logger.error(
                message_key="service_unavailable",
                function="_make_request",
                extra_message="Failed to connect to the service"
            )
            raise BusinessLogicError(
                "service_unavailable",
                details={"reason": "connection_failed"}
            )

        except HTTPError as e:
            error_key = self._get_error_key_for_status_code(e.response.status_code)
            await app_logger.error(
                message_key=error_key,
                function="_make_request",
                extra_message=f"HTTP error occurred: {str(e)}"
            )
            
            # Map status codes to appropriate exceptions
            if e.response.status_code == 400:
                raise ValidationError(error_key, details={"response": e.response.json()})
            elif e.response.status_code == 401:
                raise AuthenticationError(error_key, details={"response": e.response.json()})
            elif e.response.status_code == 404:
                raise ResourceNotFoundError(error_key, details={"response": e.response.json()})
            elif e.response.status_code == 409:
                raise BusinessLogicError(error_key, details={"response": e.response.json()})
            elif e.response.status_code >= 500:
                raise DatabaseError(error_key, details={"response": e.response.json()})
            else:
                raise BusinessLogicError(error_key, details={"response": e.response.json()})

        except json.JSONDecodeError:
            await app_logger.error(
                message_key="invalid_response",
                function="_make_request",
                extra_message="Failed to parse response JSON"
            )
            raise ValidationError(
                "invalid_response",
                details={"reason": "invalid_json"}
            )

        except Exception as e:
            await app_logger.error(
                message_key="unknown_error",
                function="_make_request",
                extra_message=f"Unexpected error: {str(e)}"
            )
            raise DatabaseError(
                "unknown_error",
                details={"error": str(e)}
            )

    def _get_error_key_for_status_code(self, status_code: int) -> str:
        """
        Map HTTP status codes to error keys.
        
        Args:
            status_code: HTTP status code
            
        Returns:
            Error key string
        """
        status_code_map = {
            400: "invalid_input",
            401: "authentication_error",
            403: "insufficient_permissions",
            404: "resource_not_found",
            409: "conflict_with_existing",
            422: "mismatch_input_schema",
            429: "login_limit_reached",
            500: "unknown_error",
            503: "service_unavailable",
            504: "service_unavailable"
        }
        return status_code_map.get(status_code, "unknown_error")

    async def get(
            self,
            endpoint: str,
            params: Optional[Dict[str, Any]] = None,
            headers: Optional[Dict[str, str]] = None,
            timeout: Optional[int] = None,
            language: str = "en"
    ) -> Dict[str, Any]:
        """Make a GET request."""
        return await self._make_request("GET", endpoint, params=params, headers=headers, timeout=timeout,
                                        language=language)

    async def post(
            self,
            endpoint: str,
            data: Optional[Dict[str, Any]] = None,
            headers: Optional[Dict[str, str]] = None,
            timeout: Optional[int] = None,
            language: str = "en"
    ) -> Dict[str, Any]:
        """Make a POST request."""
        return await self._make_request("POST", endpoint, data=data, headers=headers, timeout=timeout,
                                        language=language)

    async def put(
            self,
            endpoint: str,
            data: Optional[Dict[str, Any]] = None,
            headers: Optional[Dict[str, str]] = None,
            timeout: Optional[int] = None,
            language: str = "en"
    ) -> Dict[str, Any]:
        """Make a PUT request."""
        return await self._make_request("PUT", endpoint, data=data, headers=headers, timeout=timeout, language=language)

    async def delete(
            self,
            endpoint: str,
            headers: Optional[Dict[str, str]] = None,
            timeout: Optional[int] = None,
            language: str = "en"
    ) -> Dict[str, Any]:
        """Make a DELETE request."""
        return await self._make_request("DELETE", endpoint, headers=headers, timeout=timeout, language=language)
