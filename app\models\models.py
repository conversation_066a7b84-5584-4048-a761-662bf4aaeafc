"""Database models for GrowthHive Prospect Outreach System"""
import uuid
from datetime import datetime
from sqlalchemy import Column, String, Boolean, DateTime, Text, Numeric, Integer, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.ext.hybrid import hybrid_property
from app.core.database import Base

class User(Base):
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String(255), unique=True, nullable=False, index=True)
    mobile = Column(String(20), unique=True, index=True)
    password_hash = Column(String(255), nullable=False)
    role = Column(String(50), default="admin")
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Franchise(Base):
    __tablename__ = "franchises"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    category = Column(String(100), nullable=False)
    sub_category = Column(String(100))
    region = Column(String(100), nullable=False)
    budget = Column(Numeric(12, 2))
    brochure_url = Column(Text)
    zoho_id = Column(String(100), unique=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    documents = relationship("Document", back_populates="franchise")
    scripts = relationship("SalesScript", back_populates="franchise")

class Document(Base):
    __tablename__ = "documents"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    franchise_id = Column(UUID(as_uuid=True), ForeignKey("franchises.id"))
    filename = Column(String(255), nullable=False)
    file_path = Column(Text, nullable=False)
    file_type = Column(String(50))
    processed = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    franchise = relationship("Franchise", back_populates="documents")

class Lead(Base):
    __tablename__ = "leads"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    zoho_lead_id = Column(String(100), unique=True)
    full_name = Column(String(255), nullable=False)
    contact_number = Column(String(20), nullable=False, index=True)
    email = Column(String(255))
    location = Column(String(255))
    lead_source = Column(String(100))
    franchise_preference = Column(String(255))
    budget_preference = Column(Numeric(12, 2))
    qualification_status = Column(String(50), default="new")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    sms_conversations = relationship("SMSConversation", back_populates="lead")
    responses = relationship("LeadResponse", back_populates="lead")

class SMSConversation(Base):
    __tablename__ = "sms_conversations"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    lead_id = Column(UUID(as_uuid=True), ForeignKey("leads.id"))
    message_content = Column(Text, nullable=False)
    direction = Column(String(20), nullable=False)  # 'inbound' or 'outbound'
    sms_service_id = Column(String(100))
    status = Column(String(50))
    sent_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    lead = relationship("Lead", back_populates="sms_conversations")

class Question(Base):
    __tablename__ = "questions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    question_text = Column(Text, nullable=False)
    question_type = Column(String(50))
    is_required = Column(Boolean, default=False)
    order_sequence = Column(Integer)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    responses = relationship("LeadResponse", back_populates="question")

class LeadResponse(Base):
    __tablename__ = "lead_responses"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    lead_id = Column(UUID(as_uuid=True), ForeignKey("leads.id"))
    question_id = Column(UUID(as_uuid=True), ForeignKey("questions.id"))
    response_text = Column(Text)
    answered_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    lead = relationship("Lead", back_populates="responses")
    question = relationship("Question", back_populates="responses")

class SalesScript(Base):
    __tablename__ = "sales_scripts"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    franchise_id = Column(UUID(as_uuid=True), ForeignKey("franchises.id"))
    script_name = Column(String(255), nullable=False)
    script_content = Column(Text, nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    franchise = relationship("Franchise", back_populates="scripts")

class SystemSetting(Base):
    __tablename__ = "system_settings"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    setting_key = Column(String(100), unique=True, nullable=False)
    setting_value = Column(Text)
    setting_type = Column(String(50))  # 'holiday_message', 'agent_exception', etc.
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
