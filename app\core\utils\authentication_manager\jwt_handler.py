from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from jose import jwt, JW<PERSON>rror
from fastapi import status

from app.core.config import settings
from app.core.utils.logger.logger import logger
from app.core.utils.exception_manager.custom_exceptions import AuthenticationError
from app.core.models.pydantic_models.response_model import MessageModel


class JWTHandler:
    """JWT token handler for authentication"""
    
    def __init__(self):
        """Initialize JWT handler with settings"""
        self.secret_key = settings.JWT_SECRET_KEY
        self.algorithm = settings.JWT_ALGORITHM
        self.access_token_expire_minutes = settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES
        self.refresh_token_expire_days = settings.JWT_LONG_TOKEN_EXPIRE_DAYS
        
    async def create_access_token(
        self,
        data: Dict[str, Any],
        token_type: str = "access",
        remember_me: bool = False
    ) -> str:
        """
        Create JWT token
        
        Args:
            data: Data to encode in token
            token_type: Type of token (access or refresh)
            remember_me: Whether to create a long-lived token
            
        Returns:
            str: JWT token
        """
        try:
            # Create token data
            to_encode = data.copy()
            
            # Set expiration time
            if token_type == "access":
                if remember_me:
                    expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
                else:
                    expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
            else:  # refresh token
                expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
                
            # Add expiration and token type
            to_encode.update({
                "exp": expire,
                "type": token_type
            })
            
            # Create token
            token = jwt.encode(
                to_encode,
                self.secret_key,
                algorithm=self.algorithm
            )
            
            logger.info(
                f"Created {token_type} token",
                extra={"token_type": token_type, "expires_at": expire.isoformat()}
            )
            
            return token
            
        except Exception as e:
            logger.error(
                f"Failed to create {token_type} token",
                exc_info=True,
                extra={"token_type": token_type}
            )
            raise AuthenticationError(
                "token_creation_failed",
                message=MessageModel(
                    title="Token Creation Failed",
                    description="Failed to create authentication token. Please try again."
                ),
                details={"error": str(e)}
            )
            
    async def decode_token(self, token: str) -> Dict[str, Any]:
        """
        Decode and verify JWT token
        
        Args:
            token: JWT token to decode
            
        Returns:
            Dict[str, Any]: Decoded token data
            
        Raises:
            AuthenticationError: If token is invalid or expired
        """
        try:
            # Decode token
            payload = jwt.decode(
                token,
                self.secret_key,
                algorithms=[self.algorithm]
            )
            
            logger.info(
                "Successfully decoded token",
                extra={"token_type": payload.get("type")}
            )
            
            return payload
            
        except JWTError as e:
            logger.error(
                "Failed to decode token",
                exc_info=True
            )
            raise AuthenticationError(
                "invalid_token",
                message=MessageModel(
                    title="Invalid Token",
                    description="Invalid or expired token. Please login again."
                ),
                details={"error": str(e)}
            )
            
    async def verify_token(self, token: str) -> bool:
        """
        Verify JWT token without raising exceptions
        
        Args:
            token: JWT token to verify
            
        Returns:
            bool: True if token is valid, False otherwise
        """
        try:
            jwt.decode(
                token,
                self.secret_key,
                algorithms=[self.algorithm]
            )
            return True
        except JWTError:
            return False 